// Code generated by "stringer -type=Severity -linecomment"; DO NOT EDIT.

package log

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[SeverityUndefined-0]
	_ = x[SeverityTrace1-1]
	_ = x[SeverityTrace2-2]
	_ = x[SeverityTrace3-3]
	_ = x[SeverityTrace4-4]
	_ = x[SeverityDebug1-5]
	_ = x[SeverityDebug2-6]
	_ = x[SeverityDebug3-7]
	_ = x[SeverityDebug4-8]
	_ = x[SeverityInfo1-9]
	_ = x[SeverityInfo2-10]
	_ = x[SeverityInfo3-11]
	_ = x[SeverityInfo4-12]
	_ = x[SeverityWarn1-13]
	_ = x[SeverityWarn2-14]
	_ = x[SeverityWarn3-15]
	_ = x[SeverityWarn4-16]
	_ = x[SeverityError1-17]
	_ = x[SeverityError2-18]
	_ = x[SeverityError3-19]
	_ = x[SeverityError4-20]
	_ = x[SeverityFatal1-21]
	_ = x[SeverityFatal2-22]
	_ = x[SeverityFatal3-23]
	_ = x[SeverityFatal4-24]
}

const _Severity_name = "UNDEFINEDTRACETRACE2TRACE3TRACE4DEBUGDEBUG2DEBUG3DEBUG4INFOINFO2INFO3INFO4WARNWARN2WARN3WARN4ERRORERROR2ERROR3ERROR4FATALFATAL2FATAL3FATAL4"

var _Severity_index = [...]uint8{0, 9, 14, 20, 26, 32, 37, 43, 49, 55, 59, 64, 69, 74, 78, 83, 88, 93, 98, 104, 110, 116, 121, 127, 133, 139}

func (i Severity) String() string {
	if i < 0 || i >= Severity(len(_Severity_index)-1) {
		return "Severity(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _Severity_name[_Severity_index[i]:_Severity_index[i+1]]
}
