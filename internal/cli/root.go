package cli

import (
	"context"
	"fmt"

	"resumatter/internal/config"
	"resumatter/internal/errors"
	"resumatter/internal/security"

	"github.com/spf13/cobra"
)

// Define custom private types for context keys.
type configKeyType struct{}
type loggerKeyType struct{}

// Use variables of these types as the keys.
var configKey = configKeyType{}
var loggerKey = loggerKeyType{}

var rootCmd = &cobra.Command{
	Use:   "resumatter",
	Short: "A CLI tool for tailoring resumes using AI",
	Long: `Resumatter is a command-line tool that helps you tailor your resume
for specific job descriptions using AI. It can also evaluate tailored resumes
for accuracy and potential issues.`,
}

func Execute(ctx context.Context, cfg *config.Config, logger *errors.Logger) error {
	// Initialize Vault client and load secrets before command execution
	if err := initializeVaultSecrets(cfg, logger); err != nil {
		return fmt.Errorf("failed to initialize Vault secrets: %w", err)
	}

	// Attach the config and logger to the context, making them available to all subcommands
	ctx = context.WithValue(ctx, configKey, cfg)
	ctx = context.WithValue(ctx, loggerKey, logger)
	rootCmd.SetContext(ctx)
	return rootCmd.Execute()
}

// getConfigFromContext is a helper function to get config from context
func getConfigFromContext(ctx context.Context) (*config.Config, error) {
	if cfg, ok := ctx.Value(configKey).(*config.Config); ok {
		return cfg, nil
	}
	return nil, fmt.Errorf("config not found in context - ensure Execute() was called with proper initialization")
}

// getLoggerFromContext is a helper function to get logger from context
func getLoggerFromContext(ctx context.Context) (*errors.Logger, error) {
	if logger, ok := ctx.Value(loggerKey).(*errors.Logger); ok {
		return logger, nil
	}
	return nil, fmt.Errorf("logger not found in context - ensure Execute() was called with proper initialization")
}

// initializeVaultSecrets initializes Vault client and loads secrets into config
func initializeVaultSecrets(cfg *config.Config, logger *errors.Logger) error {
	if !cfg.Vault.Enabled {
		logger.Debug("Vault integration disabled, skipping secret loading")
		return nil
	}

	logger.Info("Initializing Vault client for secret loading")

	// Use the existing VaultConfig from the security package (no conversion needed)
	vaultClient, err := security.NewVaultClient(cfg.Vault, logger)
	if err != nil {
		return fmt.Errorf("failed to create Vault client: %w", err)
	}

	if vaultClient == nil {
		logger.Debug("Vault client not initialized (disabled)")
		return nil
	}

	// Load secrets and apply to configuration
	if err := loadAndApplyVaultSecrets(vaultClient, cfg, logger); err != nil {
		return err
	}

	// Validate that we have required secrets after Vault loading
	return validatePostVaultSecrets(cfg, logger)
}

// loadAndApplyVaultSecrets loads secrets from Vault and applies them to config
func loadAndApplyVaultSecrets(vaultClient *security.VaultClient, cfg *config.Config, logger *errors.Logger) error {
	// Load AI API Keys (operation-specific with fallbacks)
	if err := loadAIAPIKeys(vaultClient, cfg, logger); err != nil {
		logger.LogError(err, "Failed to load AI API keys from Vault")
		// Don't fail startup, allow fallback to env vars/config
	}

	// Load Server API Keys if configured
	if cfg.Vault.Secrets.APIKeys != "" {
		if err := loadServerAPIKeys(vaultClient, cfg, logger); err != nil {
			logger.LogError(err, "Failed to load server API keys from Vault")
			// Don't fail startup, allow fallback to env vars/config
		}
	}

	logger.Info("Vault secrets loaded successfully")
	return nil
}

// loadAIAPIKeys loads AI API keys from Vault with operation-specific support
func loadAIAPIKeys(vaultClient *security.VaultClient, cfg *config.Config, logger *errors.Logger) error {
	// Check if any AI secrets are configured
	hasAISecrets := cfg.Vault.Secrets.AI.Default != "" ||
		cfg.Vault.Secrets.AI.Tailor != "" ||
		cfg.Vault.Secrets.AI.Evaluate != "" ||
		cfg.Vault.Secrets.AI.Analyze != ""

	if !hasAISecrets {
		logger.Debug("No AI secrets configured in Vault, skipping AI API key loading")
		return nil
	}

	logger.Info("Loading AI API keys from Vault with operation-specific support")

	// Load operation-specific API keys
	operations := []struct {
		name   string
		target *string
	}{
		{"tailor", &cfg.AI.Tailor.APIKey},
		{"evaluate", &cfg.AI.Evaluate.APIKey},
		{"analyze", &cfg.AI.Analyze.APIKey},
	}

	for _, op := range operations {
		if *op.target == "" { // Only load if not already set (env vars take precedence)
			apiKey, err := vaultClient.GetAIOperationAPIKey(op.name)
			if err != nil {
				logger.Debug("Failed to load operation-specific API key from Vault",
					"operation", op.name, "error", err)
				continue
			}
			*op.target = apiKey
			logger.Debug("Successfully loaded operation-specific API key from Vault", "operation", op.name)
		}
	}

	// Load global AI API key if not set
	if cfg.AI.APIKey == "" {
		// Try to get a global API key (use tailor as fallback since it's the primary operation)
		apiKey, err := vaultClient.GetAIOperationAPIKey("tailor")
		if err != nil {
			logger.Debug("Failed to load global AI API key from Vault", "error", err)
		} else {
			cfg.AI.APIKey = apiKey
			logger.Debug("Successfully loaded global AI API key from Vault")
		}
	}

	logger.Debug("AI API key loading from Vault completed")
	return nil
}

// loadServerAPIKeys loads server API keys from Vault
func loadServerAPIKeys(vaultClient *security.VaultClient, cfg *config.Config, logger *errors.Logger) error {
	// Use configured path and key for server API keys
	path := cfg.Vault.Secrets.APIKeys
	key := cfg.Vault.SecretKeys.APIKeys

	// Fallback to default key name if not configured
	if key == "" {
		key = "apiKeys"
	}

	apiKeys, err := vaultClient.GetStringSliceSecret(path, key)
	if err != nil {
		return fmt.Errorf("failed to get server API keys from %s:%s: %w", path, key, err)
	}

	// Apply to server config with precedence (only if not already set)
	if len(cfg.Server.APIKeys) == 0 {
		cfg.Server.APIKeys = apiKeys
		logger.Debug("Server API keys loaded from Vault", "count", len(apiKeys))
	}

	return nil
}

// validatePostVaultSecrets validates that required secrets are available after Vault loading
func validatePostVaultSecrets(cfg *config.Config, logger *errors.Logger) error {
	// Check if AI API key is still missing after Vault loading
	if cfg.AI.APIKey == "" {
		if cfg.Vault.Enabled && isAnyAISecretConfigured(cfg) {
			return fmt.Errorf("Vault is enabled and configured for AI API keys, but failed to load any secret from configured paths")
		}
		return fmt.Errorf("AI API key is required (set RESUMATTER_AI_APIKEY environment variable or configure Vault)")
	}

	logger.Debug("Post-Vault validation passed",
		"ai_api_key_configured", cfg.AI.APIKey != "",
		"tailor_api_key_configured", cfg.AI.Tailor.APIKey != "",
		"evaluate_api_key_configured", cfg.AI.Evaluate.APIKey != "",
		"analyze_api_key_configured", cfg.AI.Analyze.APIKey != "")
	return nil
}

// isAnyAISecretConfigured checks if any AI secret paths are configured in Vault
func isAnyAISecretConfigured(cfg *config.Config) bool {
	return cfg.Vault.Secrets.AI.Default != "" ||
		cfg.Vault.Secrets.AI.Tailor != "" ||
		cfg.Vault.Secrets.AI.Evaluate != "" ||
		cfg.Vault.Secrets.AI.Analyze != ""
}

func init() {
	rootCmd.AddCommand(tailorCmd)
	rootCmd.AddCommand(evaluateCmd)
	rootCmd.AddCommand(analyzeCmd)
	rootCmd.AddCommand(versionCmd)
	rootCmd.AddCommand(serveCmd)
}
