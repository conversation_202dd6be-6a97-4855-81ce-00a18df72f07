package errors

import (
	"context"
	"crypto/sha256"
	"fmt"
	"log/slog"
	"os"
	"runtime"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/baggage"
	"go.opentelemetry.io/otel/log"
	"go.opentelemetry.io/otel/log/global"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/trace"
)

// ErrorType represents different categories of errors
type ErrorType string

const (
	ErrorTypeValidation ErrorType = "validation"
	ErrorTypeIO         ErrorType = "io"
	ErrorTypeAI         ErrorType = "ai"
	ErrorTypeNetwork    ErrorType = "network"
	ErrorTypeConfig     ErrorType = "config"
	ErrorTypeInternal   ErrorType = "internal"
)

// ErrorSeverity represents the severity level of an error
type ErrorSeverity string

const (
	ErrorSeverityLow      ErrorSeverity = "low"      // Minor issues, warnings
	ErrorSeverityMedium   ErrorSeverity = "medium"   // Recoverable errors
	ErrorSeverityHigh     ErrorSeverity = "high"     // Serious errors affecting functionality
	ErrorSeverityCritical ErrorSeverity = "critical" // System-threatening errors
)

// AppError represents a structured application error
type AppError struct {
	Type        ErrorType      `json:"type"`
	Code        string         `json:"code"`
	Message     string         `json:"message"`
	Cause       error          `json:"cause,omitempty"`
	Context     map[string]any `json:"context,omitempty"`
	Severity    ErrorSeverity  `json:"severity,omitempty"`
	Fingerprint string         `json:"fingerprint,omitempty"` // For error deduplication
	Timestamp   time.Time      `json:"timestamp,omitempty"`   // When the error occurred
	Component   string         `json:"component,omitempty"`   // Which component/module generated the error
	Operation   string         `json:"operation,omitempty"`   // What operation was being performed
	UserID      string         `json:"user_id,omitempty"`     // User context if available
	RequestID   string         `json:"request_id,omitempty"`  // Request correlation ID
}

func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

func (e *AppError) Unwrap() error {
	return e.Cause
}

// newAppError is an unexported helper to create AppError instances
func newAppError(typ ErrorType, code, message string, cause error) *AppError {
	err := &AppError{
		Type:        typ,
		Code:        code,
		Message:     message,
		Cause:       cause,
		Severity:    getDefaultSeverity(typ),
		Timestamp:   time.Now(),
		Fingerprint: generateErrorFingerprint(typ, code, message),
	}
	return err
}

// newAppErrorWithSeverity creates an AppError with specified severity
func newAppErrorWithSeverity(typ ErrorType, code, message string, cause error, severity ErrorSeverity) *AppError {
	err := newAppError(typ, code, message, cause)
	err.Severity = severity
	err.Fingerprint = generateErrorFingerprint(typ, code, message) // Regenerate with severity
	return err
}

// getDefaultSeverity returns the default severity for an error type
func getDefaultSeverity(typ ErrorType) ErrorSeverity {
	switch typ {
	case ErrorTypeValidation:
		return ErrorSeverityMedium
	case ErrorTypeIO:
		return ErrorSeverityMedium
	case ErrorTypeAI:
		return ErrorSeverityHigh
	case ErrorTypeNetwork:
		return ErrorSeverityMedium
	case ErrorTypeConfig:
		return ErrorSeverityHigh
	case ErrorTypeInternal:
		return ErrorSeverityCritical
	default:
		return ErrorSeverityMedium
	}
}

// generateErrorFingerprint creates a unique fingerprint for error deduplication
func generateErrorFingerprint(typ ErrorType, code, message string) string {
	// Create a hash of type, code, and message for deduplication
	content := fmt.Sprintf("%s:%s:%s", typ, code, message)
	hash := sha256.Sum256([]byte(content))
	return fmt.Sprintf("%x", hash[:8]) // Use first 8 bytes for shorter fingerprint
}

// Error constructors for different types
func NewValidationError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeValidation, code, message, cause)
}

func NewValidationErrorWithSeverity(code, message string, cause error, severity ErrorSeverity) *AppError {
	return newAppErrorWithSeverity(ErrorTypeValidation, code, message, cause, severity)
}

func NewIOError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeIO, code, message, cause)
}

func NewIOErrorWithSeverity(code, message string, cause error, severity ErrorSeverity) *AppError {
	return newAppErrorWithSeverity(ErrorTypeIO, code, message, cause, severity)
}

func NewAIError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeAI, code, message, cause)
}

func NewAIErrorWithSeverity(code, message string, cause error, severity ErrorSeverity) *AppError {
	return newAppErrorWithSeverity(ErrorTypeAI, code, message, cause, severity)
}

func NewNetworkError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeNetwork, code, message, cause)
}

func NewNetworkErrorWithSeverity(code, message string, cause error, severity ErrorSeverity) *AppError {
	return newAppErrorWithSeverity(ErrorTypeNetwork, code, message, cause, severity)
}

func NewConfigError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeConfig, code, message, cause)
}

func NewConfigErrorWithSeverity(code, message string, cause error, severity ErrorSeverity) *AppError {
	return newAppErrorWithSeverity(ErrorTypeConfig, code, message, cause, severity)
}

func NewInternalError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeInternal, code, message, cause)
}

func NewInternalErrorWithSeverity(code, message string, cause error, severity ErrorSeverity) *AppError {
	return newAppErrorWithSeverity(ErrorTypeInternal, code, message, cause, severity)
}

// WithContext adds context to an error
func (e *AppError) WithContext(key string, value any) *AppError {
	if e.Context == nil {
		e.Context = make(map[string]any)
	}
	e.Context[key] = value
	return e
}

// WithSeverity sets the error severity
func (e *AppError) WithSeverity(severity ErrorSeverity) *AppError {
	e.Severity = severity
	return e
}

// WithComponent sets the component that generated the error
func (e *AppError) WithComponent(component string) *AppError {
	e.Component = component
	return e
}

// WithOperation sets the operation that was being performed
func (e *AppError) WithOperation(operation string) *AppError {
	e.Operation = operation
	return e
}

// WithUserID sets the user context
func (e *AppError) WithUserID(userID string) *AppError {
	e.UserID = userID
	return e
}

// WithRequestID sets the request correlation ID
func (e *AppError) WithRequestID(requestID string) *AppError {
	e.RequestID = requestID
	return e
}

// WithCorrelation sets both user and request IDs for full correlation
func (e *AppError) WithCorrelation(userID, requestID string) *AppError {
	e.UserID = userID
	e.RequestID = requestID
	return e
}

// GetStackTrace captures the current stack trace
func (e *AppError) GetStackTrace() []string {
	var stack []string
	pc := make([]uintptr, 10)   // Capture up to 10 frames
	n := runtime.Callers(2, pc) // Skip this function and the caller
	frames := runtime.CallersFrames(pc[:n])

	for {
		frame, more := frames.Next()
		if !strings.Contains(frame.File, "runtime/") {
			stack = append(stack, fmt.Sprintf("%s:%d %s", frame.File, frame.Line, frame.Function))
		}
		if !more {
			break
		}
	}
	return stack
}

// extractCorrelationContext extracts comprehensive correlation information from context
func extractCorrelationContext(ctx context.Context, config LoggerConfig) *CorrelationContext {
	correlation := &CorrelationContext{
		ServiceName:  config.ServiceName,
		BaggageItems: make(map[string]string),
	}

	// Extract trace context
	if span := trace.SpanFromContext(ctx); span.SpanContext().IsValid() {
		spanCtx := span.SpanContext()
		correlation.TraceID = spanCtx.TraceID().String()
		correlation.SpanID = spanCtx.SpanID().String()
		correlation.TraceFlags = spanCtx.TraceFlags().String()

		// Extract span information if available
		if readOnlySpan, ok := span.(interface {
			Name() string
			SpanKind() trace.SpanKind
		}); ok {
			correlation.SpanName = readOnlySpan.Name()
			correlation.SpanKind = readOnlySpan.SpanKind().String()
		}
	}

	// Extract baggage if enabled
	if config.EnableBaggage {
		if bag := baggage.FromContext(ctx); bag.Len() > 0 {
			for _, member := range bag.Members() {
				correlation.BaggageItems[member.Key()] = member.Value()

				// Extract well-known correlation IDs from baggage
				switch member.Key() {
				case "user.id", "user_id":
					correlation.UserID = member.Value()
				case "request.id", "request_id":
					correlation.RequestID = member.Value()
				case "session.id", "session_id":
					correlation.SessionID = member.Value()
				case "operation.name", "operation_name":
					correlation.OperationName = member.Value()
				}
			}
		}
	}

	// Extract correlation IDs from context values (fallback)
	if correlation.UserID == "" {
		if userID, ok := ctx.Value("user_id").(string); ok {
			correlation.UserID = userID
		}
	}
	if correlation.RequestID == "" {
		if requestID, ok := ctx.Value("request_id").(string); ok {
			correlation.RequestID = requestID
		}
	}
	if correlation.SessionID == "" {
		if sessionID, ok := ctx.Value("session_id").(string); ok {
			correlation.SessionID = sessionID
		}
	}

	return correlation
}

// addCorrelationAttributes adds correlation context as OpenTelemetry attributes
func addCorrelationAttributes(correlation *CorrelationContext, attrs []attribute.KeyValue) []attribute.KeyValue {
	if correlation == nil {
		return attrs
	}

	// Add trace context
	if correlation.TraceID != "" {
		attrs = append(attrs, attribute.String("trace.id", correlation.TraceID))
	}
	if correlation.SpanID != "" {
		attrs = append(attrs, attribute.String("span.id", correlation.SpanID))
	}
	if correlation.TraceFlags != "" {
		attrs = append(attrs, attribute.String("trace.flags", correlation.TraceFlags))
	}
	if correlation.SpanName != "" {
		attrs = append(attrs, attribute.String("span.name", correlation.SpanName))
	}
	if correlation.SpanKind != "" {
		attrs = append(attrs, attribute.String("span.kind", correlation.SpanKind))
	}

	// Add service context
	if correlation.ServiceName != "" {
		attrs = append(attrs, attribute.String("service.name", correlation.ServiceName))
	}
	if correlation.OperationName != "" {
		attrs = append(attrs, attribute.String("operation.name", correlation.OperationName))
	}

	// Add correlation IDs
	if correlation.UserID != "" {
		attrs = append(attrs, attribute.String("user.id", correlation.UserID))
	}
	if correlation.RequestID != "" {
		attrs = append(attrs, attribute.String("request.id", correlation.RequestID))
	}
	if correlation.SessionID != "" {
		attrs = append(attrs, attribute.String("session.id", correlation.SessionID))
	}

	// Add baggage items
	for key, value := range correlation.BaggageItems {
		attrs = append(attrs, attribute.String(fmt.Sprintf("baggage.%s", key), value))
	}

	return attrs
}

// Logger wraps slog with application-specific methods and optional OpenTelemetry log bridge
type Logger struct {
	logger     *slog.Logger
	otelLogger log.Logger
	config     LoggerConfig
}

// LoggerConfig holds configuration for the logger bridge
type LoggerConfig struct {
	EnableOTelLogs     bool
	EnableTraceContext bool
	EnableBaggage      bool
	ServiceName        string
}

// CorrelationContext holds correlation information extracted from context
type CorrelationContext struct {
	TraceID       string
	SpanID        string
	TraceFlags    string
	SpanName      string
	SpanKind      string
	ServiceName   string
	OperationName string
	UserID        string
	RequestID     string
	SessionID     string
	BaggageItems  map[string]string
}

// NewLogger creates a new structured logger
func NewLogger(level slog.Level) *Logger {
	return NewLoggerWithConfig(level, LoggerConfig{
		EnableOTelLogs:     false, // Default to slog only for backward compatibility
		EnableTraceContext: false,
		EnableBaggage:      false,
		ServiceName:        "resumatter",
	})
}

// NewLoggerWithConfig creates a new structured logger with OpenTelemetry bridge configuration
func NewLoggerWithConfig(level slog.Level, config LoggerConfig) *Logger {
	opts := &slog.HandlerOptions{
		Level: level,
	}

	handler := slog.NewJSONHandler(os.Stdout, opts)
	slogLogger := slog.New(handler)

	var otelLogger log.Logger
	if config.EnableOTelLogs {
		otelLogger = global.Logger(config.ServiceName)
	}

	return &Logger{
		logger:     slogLogger,
		otelLogger: otelLogger,
		config:     config,
	}
}

// emitOTelLog emits a log record to OpenTelemetry if enabled
func (l *Logger) emitOTelLog(ctx context.Context, severity log.Severity, message string, attrs []attribute.KeyValue) {
	if l.otelLogger == nil || !l.config.EnableOTelLogs {
		return
	}

	var record log.Record
	record.SetTimestamp(time.Now())
	record.SetSeverity(severity)
	record.SetBody(log.StringValue(message))

	// Add enhanced correlation context if enabled
	if l.config.EnableTraceContext {
		correlation := extractCorrelationContext(ctx, l.config)
		attrs = addCorrelationAttributes(correlation, attrs)
	}

	// Convert attribute.KeyValue to log.KeyValue and add to record
	logAttrs := make([]log.KeyValue, len(attrs))
	for i, attr := range attrs {
		logAttrs[i] = log.KeyValueFromAttribute(attr)
	}
	record.AddAttributes(logAttrs...)

	l.otelLogger.Emit(ctx, record)
}

// emitErrorEvent emits a structured error event for enhanced observability
func (l *Logger) emitErrorEvent(ctx context.Context, appErr *AppError, message string, baseAttrs []attribute.KeyValue) {
	if l.otelLogger == nil || !l.config.EnableOTelLogs {
		return
	}

	// Create an error event record with enhanced attributes
	var record log.Record
	record.SetTimestamp(time.Now())
	record.SetSeverity(l.mapErrorSeverityToLogSeverity(appErr.Severity))

	// Create structured error event body
	eventBody := fmt.Sprintf("ERROR_EVENT: %s [%s:%s]", message, appErr.Type, appErr.Code)
	record.SetBody(log.StringValue(eventBody))

	// Enhanced error event attributes
	eventAttrs := []attribute.KeyValue{
		attribute.String("event.type", "error"),
		attribute.String("event.name", "application_error"),
		attribute.String("error.fingerprint", appErr.Fingerprint),
		attribute.String("error.severity", string(appErr.Severity)),
		attribute.String("error.type", string(appErr.Type)),
		attribute.String("error.code", appErr.Code),
		attribute.Bool("error.is_structured", true),
	}

	// Add timing information
	if !appErr.Timestamp.IsZero() {
		eventAttrs = append(eventAttrs,
			attribute.String("error.occurred_at", appErr.Timestamp.Format(time.RFC3339)),
			attribute.Int64("error.age_ms", time.Since(appErr.Timestamp).Milliseconds()),
		)
	}

	// Add correlation context
	if appErr.Component != "" || appErr.Operation != "" {
		eventAttrs = append(eventAttrs, attribute.String("error.context",
			fmt.Sprintf("%s:%s", appErr.Component, appErr.Operation)))
	}

	// Combine with base attributes
	eventAttrs = append(eventAttrs, baseAttrs...)

	// Add enhanced correlation context if enabled
	if l.config.EnableTraceContext {
		correlation := extractCorrelationContext(ctx, l.config)
		eventAttrs = addCorrelationAttributes(correlation, eventAttrs)
	}

	// Convert to log attributes and emit
	logAttrs := make([]log.KeyValue, len(eventAttrs))
	for i, attr := range eventAttrs {
		logAttrs[i] = log.KeyValueFromAttribute(attr)
	}
	record.AddAttributes(logAttrs...)

	l.otelLogger.Emit(ctx, record)
}

// mapErrorSeverityToLogSeverity maps AppError severity to OpenTelemetry log severity
func (l *Logger) mapErrorSeverityToLogSeverity(severity ErrorSeverity) log.Severity {
	switch severity {
	case ErrorSeverityLow:
		return log.SeverityWarn
	case ErrorSeverityMedium:
		return log.SeverityError
	case ErrorSeverityHigh:
		return log.SeverityError
	case ErrorSeverityCritical:
		return log.SeverityFatal
	default:
		return log.SeverityError
	}
}

// convertArgsToAttributes converts slog-style args to OpenTelemetry attributes
func (l *Logger) convertArgsToAttributes(args []any) []attribute.KeyValue {
	var attrs []attribute.KeyValue

	// Process args in pairs (key, value)
	for i := 0; i < len(args)-1; i += 2 {
		if key, ok := args[i].(string); ok {
			value := args[i+1]
			attrs = append(attrs, convertValueToAttribute(key, value))
		}
	}

	return attrs
}

// convertValueToAttribute converts a value to an OpenTelemetry attribute
func convertValueToAttribute(key string, value any) attribute.KeyValue {
	switch v := value.(type) {
	case string:
		return attribute.String(key, v)
	case int:
		return attribute.Int(key, v)
	case int64:
		return attribute.Int64(key, v)
	case float64:
		return attribute.Float64(key, v)
	case bool:
		return attribute.Bool(key, v)
	case ErrorType:
		return attribute.String(key, string(v))
	default:
		// Convert to string for unsupported types
		return attribute.String(key, fmt.Sprintf("%v", v))
	}
}

// LogError logs an application error with appropriate level and context
func (l *Logger) LogError(err error, message string, args ...any) {
	l.LogErrorWithContext(context.Background(), err, message, args...)
}

// LogErrorWithContext logs an application error with context for trace correlation
func (l *Logger) LogErrorWithContext(ctx context.Context, err error, message string, args ...any) {
	var logArgs []any
	var otelAttrs []attribute.KeyValue

	if appErr, ok := err.(*AppError); ok {
		// Enhanced slog attributes
		logArgs = []any{
			"error_type", appErr.Type,
			"error_code", appErr.Code,
			"error_message", appErr.Message,
			"error_severity", appErr.Severity,
			"error_fingerprint", appErr.Fingerprint,
		}

		// Enhanced OpenTelemetry attributes
		otelAttrs = []attribute.KeyValue{
			attribute.String("error.type", string(appErr.Type)),
			attribute.String("error.code", appErr.Code),
			attribute.String("error.message", appErr.Message),
			attribute.String("error.severity", string(appErr.Severity)),
			attribute.String("error.fingerprint", appErr.Fingerprint),
		}

		// Add timestamp if available
		if !appErr.Timestamp.IsZero() {
			logArgs = append(logArgs, "error_timestamp", appErr.Timestamp.Format(time.RFC3339))
			otelAttrs = append(otelAttrs, attribute.String("error.timestamp", appErr.Timestamp.Format(time.RFC3339)))
		}

		// Add component and operation context
		if appErr.Component != "" {
			logArgs = append(logArgs, "error_component", appErr.Component)
			otelAttrs = append(otelAttrs, attribute.String("error.component", appErr.Component))
		}
		if appErr.Operation != "" {
			logArgs = append(logArgs, "error_operation", appErr.Operation)
			otelAttrs = append(otelAttrs, attribute.String("error.operation", appErr.Operation))
		}

		// Add correlation IDs
		if appErr.UserID != "" {
			logArgs = append(logArgs, "user_id", appErr.UserID)
			otelAttrs = append(otelAttrs, attribute.String("user.id", appErr.UserID))
		}
		if appErr.RequestID != "" {
			logArgs = append(logArgs, "request_id", appErr.RequestID)
			otelAttrs = append(otelAttrs, attribute.String("request.id", appErr.RequestID))
		}

		// Add stack trace for critical errors
		if appErr.Severity == ErrorSeverityCritical {
			stackTrace := appErr.GetStackTrace()
			if len(stackTrace) > 0 {
				logArgs = append(logArgs, "stack_trace", strings.Join(stackTrace, "\n"))
				otelAttrs = append(otelAttrs, attribute.String("error.stack_trace", strings.Join(stackTrace, "\n")))
			}
		}

		// Add context if available
		for key, value := range appErr.Context {
			logArgs = append(logArgs, key, value)
			otelAttrs = append(otelAttrs, convertValueToAttribute(key, value))
		}

		// Add additional args
		logArgs = append(logArgs, args...)
		otelAttrs = append(otelAttrs, l.convertArgsToAttributes(args)...)

		// Add the underlying error if present
		if appErr.Cause != nil {
			logArgs = append(logArgs, "error_cause", appErr.Cause.Error())
			otelAttrs = append(otelAttrs, attribute.String("error.cause", appErr.Cause.Error()))
		}

		// Emit error event for observability
		l.emitErrorEvent(ctx, appErr, message, otelAttrs)
	} else {
		// Regular error
		logArgs = append([]any{"error", err.Error()}, args...)
		otelAttrs = []attribute.KeyValue{
			attribute.String("error", err.Error()),
			attribute.String("error.type", "generic"),
			attribute.String("error.severity", string(ErrorSeverityMedium)),
		}
		otelAttrs = append(otelAttrs, l.convertArgsToAttributes(args)...)
	}

	// Log to slog (existing behavior)
	l.logger.Error(message, logArgs...)

	// Log to OpenTelemetry if enabled
	l.emitOTelLog(ctx, log.SeverityError, message, otelAttrs)
}

func (l *Logger) Info(message string, args ...any) {
	l.InfoWithContext(context.Background(), message, args...)
}

func (l *Logger) InfoWithContext(ctx context.Context, message string, args ...any) {
	// Log to slog (existing behavior)
	l.logger.Info(message, args...)

	// Log to OpenTelemetry if enabled
	otelAttrs := l.convertArgsToAttributes(args)
	l.emitOTelLog(ctx, log.SeverityInfo, message, otelAttrs)
}

func (l *Logger) Debug(message string, args ...any) {
	l.DebugWithContext(context.Background(), message, args...)
}

func (l *Logger) DebugWithContext(ctx context.Context, message string, args ...any) {
	// Log to slog (existing behavior)
	l.logger.Debug(message, args...)

	// Log to OpenTelemetry if enabled
	otelAttrs := l.convertArgsToAttributes(args)
	l.emitOTelLog(ctx, log.SeverityDebug, message, otelAttrs)
}

func (l *Logger) Warn(message string, args ...any) {
	l.WarnWithContext(context.Background(), message, args...)
}

func (l *Logger) WarnWithContext(ctx context.Context, message string, args ...any) {
	// Log to slog (existing behavior)
	l.logger.Warn(message, args...)

	// Log to OpenTelemetry if enabled
	otelAttrs := l.convertArgsToAttributes(args)
	l.emitOTelLog(ctx, log.SeverityWarn, message, otelAttrs)
}

// New creates a new logger instance
func New(level string) (*Logger, error) {
	return NewWithConfig(level, LoggerConfig{
		EnableOTelLogs:     false, // Default to slog only for backward compatibility
		EnableTraceContext: false,
		EnableBaggage:      false,
		ServiceName:        "resumatter",
	})
}

// NewWithConfig creates a new logger instance with OpenTelemetry bridge configuration
func NewWithConfig(level string, config LoggerConfig) (*Logger, error) {
	var slogLevel slog.Level
	switch level {
	case "debug":
		slogLevel = slog.LevelDebug
	case "info":
		slogLevel = slog.LevelInfo
	case "warn":
		slogLevel = slog.LevelWarn
	case "error":
		slogLevel = slog.LevelError
	default:
		return nil, fmt.Errorf("invalid log level: %s", level)
	}

	return NewLoggerWithConfig(slogLevel, config), nil
}

// NewLoggerFromObservabilityConfig creates a logger from observability configuration
// This function bridges the gap between the observability config and the logger
func NewLoggerFromObservabilityConfig(level string, serviceName string, logsEnabled bool, traceContextEnabled bool, baggageEnabled bool) (*Logger, error) {
	config := LoggerConfig{
		EnableOTelLogs:     logsEnabled,
		EnableTraceContext: traceContextEnabled,
		EnableBaggage:      baggageEnabled,
		ServiceName:        serviceName,
	}

	return NewWithConfig(level, config)
}

// NewStructuredError creates a fully structured AppError with all context
func NewStructuredError(typ ErrorType, code, message string, cause error, severity ErrorSeverity, component, operation string) *AppError {
	err := newAppErrorWithSeverity(typ, code, message, cause, severity)
	err.Component = component
	err.Operation = operation
	return err
}

// NewErrorWithCorrelation creates an AppError with correlation context
func NewErrorWithCorrelation(typ ErrorType, code, message string, cause error, userID, requestID string) *AppError {
	err := newAppError(typ, code, message, cause)
	err.UserID = userID
	err.RequestID = requestID
	return err
}

// LogStructuredError is a convenience method for logging errors with full context
func (l *Logger) LogStructuredError(ctx context.Context, typ ErrorType, code, message, logMessage string, severity ErrorSeverity, component, operation string, args ...any) {
	err := NewStructuredError(typ, code, message, nil, severity, component, operation)
	l.LogErrorWithContext(ctx, err, logMessage, args...)
}

// LogErrorWithMetrics logs an error and optionally increments error metrics
func (l *Logger) LogErrorWithMetrics(ctx context.Context, err error, message string, incrementMetrics bool, args ...any) {
	l.LogErrorWithContext(ctx, err, message, args...)

	// TODO: Add metrics integration when metrics are available in context
	// This would increment error counters by type, severity, component, etc.
	if incrementMetrics {
		// Placeholder for future metrics integration
		// metrics.IncrementErrorCounter(err.Type, err.Severity, err.Component)
	}
}

// ContextWithCorrelation creates a context with correlation information
func ContextWithCorrelation(ctx context.Context, userID, requestID, sessionID string) context.Context {
	// Add to context values
	ctx = context.WithValue(ctx, "user_id", userID)
	ctx = context.WithValue(ctx, "request_id", requestID)
	ctx = context.WithValue(ctx, "session_id", sessionID)

	// Add to baggage for cross-service propagation
	var members []baggage.Member

	if userID != "" {
		if member, err := baggage.NewMember("user.id", userID); err == nil {
			members = append(members, member)
		}
	}
	if requestID != "" {
		if member, err := baggage.NewMember("request.id", requestID); err == nil {
			members = append(members, member)
		}
	}
	if sessionID != "" {
		if member, err := baggage.NewMember("session.id", sessionID); err == nil {
			members = append(members, member)
		}
	}

	if len(members) > 0 {
		if bag, err := baggage.New(members...); err == nil {
			ctx = baggage.ContextWithBaggage(ctx, bag)
		}
	}

	return ctx
}

// ContextWithUserID creates a context with user correlation
func ContextWithUserID(ctx context.Context, userID string) context.Context {
	return ContextWithCorrelation(ctx, userID, "", "")
}

// ContextWithRequestID creates a context with request correlation
func ContextWithRequestID(ctx context.Context, requestID string) context.Context {
	return ContextWithCorrelation(ctx, "", requestID, "")
}

// ContextWithOperation creates a context with operation information
func ContextWithOperation(ctx context.Context, operationName string) context.Context {
	ctx = context.WithValue(ctx, "operation_name", operationName)

	// Add to baggage
	if member, err := baggage.NewMember("operation.name", operationName); err == nil {
		if bag := baggage.FromContext(ctx); bag.Len() > 0 {
			if newBag, err := bag.SetMember(member); err == nil {
				ctx = baggage.ContextWithBaggage(ctx, newBag)
			}
		} else {
			if newBag, err := baggage.New(member); err == nil {
				ctx = baggage.ContextWithBaggage(ctx, newBag)
			}
		}
	}

	return ctx
}

// ExtractCorrelationFromHeaders extracts correlation information from HTTP headers
func ExtractCorrelationFromHeaders(headers map[string]string) (userID, requestID, sessionID string) {
	// Common header patterns for correlation IDs
	headerMappings := map[string]*string{
		"x-user-id":        &userID,
		"user-id":          &userID,
		"x-request-id":     &requestID,
		"request-id":       &requestID,
		"x-correlation-id": &requestID,
		"correlation-id":   &requestID,
		"x-session-id":     &sessionID,
		"session-id":       &sessionID,
	}

	for header, value := range headers {
		if target, exists := headerMappings[strings.ToLower(header)]; exists && value != "" {
			*target = value
		}
	}

	return userID, requestID, sessionID
}

// InjectCorrelationToHeaders injects correlation information into HTTP headers
func InjectCorrelationToHeaders(ctx context.Context, headers map[string]string) {
	correlation := extractCorrelationContext(ctx, LoggerConfig{EnableBaggage: true})

	if correlation.UserID != "" {
		headers["X-User-ID"] = correlation.UserID
	}
	if correlation.RequestID != "" {
		headers["X-Request-ID"] = correlation.RequestID
	}
	if correlation.SessionID != "" {
		headers["X-Session-ID"] = correlation.SessionID
	}
	if correlation.TraceID != "" {
		headers["X-Trace-ID"] = correlation.TraceID
	}
	if correlation.SpanID != "" {
		headers["X-Span-ID"] = correlation.SpanID
	}
}

// PropagateCorrelation creates a new context with OpenTelemetry propagation
func PropagateCorrelation(ctx context.Context, headers map[string]string) context.Context {
	// Use OpenTelemetry propagator to extract trace context from headers
	propagator := propagation.TraceContext{}
	carrier := propagation.MapCarrier(headers)

	// Extract trace context
	ctx = propagator.Extract(ctx, carrier)

	// Extract correlation IDs from headers
	userID, requestID, sessionID := ExtractCorrelationFromHeaders(headers)

	// Add correlation to context
	if userID != "" || requestID != "" || sessionID != "" {
		ctx = ContextWithCorrelation(ctx, userID, requestID, sessionID)
	}

	return ctx
}

// Common error codes
const (
	ErrCodeFileNotFound    = "FILE_NOT_FOUND"
	ErrCodeFileNotReadable = "FILE_NOT_READABLE"
	ErrCodeInvalidFormat   = "INVALID_FORMAT"
	ErrCodeAIServiceFailed = "AI_SERVICE_FAILED"
	ErrCodeAITimeout       = "AI_TIMEOUT"
	ErrCodeInvalidRequest  = "INVALID_REQUEST"
	ErrCodeMissingAPIKey   = "MISSING_API_KEY"
	ErrCodeNetworkTimeout  = "NETWORK_TIMEOUT"
	ErrCodeInvalidConfig   = "INVALID_CONFIG"
)
