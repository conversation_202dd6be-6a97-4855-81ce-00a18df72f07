package errors

import (
	"context"
	"crypto/sha256"
	"fmt"
	"log/slog"
	"os"
	"runtime"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/log"
	"go.opentelemetry.io/otel/log/global"
	"go.opentelemetry.io/otel/trace"
)

// ErrorType represents different categories of errors
type ErrorType string

const (
	ErrorTypeValidation ErrorType = "validation"
	ErrorTypeIO         ErrorType = "io"
	ErrorTypeAI         ErrorType = "ai"
	ErrorTypeNetwork    ErrorType = "network"
	ErrorTypeConfig     ErrorType = "config"
	ErrorTypeInternal   ErrorType = "internal"
)

// ErrorSeverity represents the severity level of an error
type ErrorSeverity string

const (
	ErrorSeverityLow      ErrorSeverity = "low"      // Minor issues, warnings
	ErrorSeverityMedium   ErrorSeverity = "medium"   // Recoverable errors
	ErrorSeverityHigh     ErrorSeverity = "high"     // Serious errors affecting functionality
	ErrorSeverityCritical ErrorSeverity = "critical" // System-threatening errors
)

// AppError represents a structured application error
type AppError struct {
	Type        ErrorType      `json:"type"`
	Code        string         `json:"code"`
	Message     string         `json:"message"`
	Cause       error          `json:"cause,omitempty"`
	Context     map[string]any `json:"context,omitempty"`
	Severity    ErrorSeverity  `json:"severity,omitempty"`
	Fingerprint string         `json:"fingerprint,omitempty"` // For error deduplication
	Timestamp   time.Time      `json:"timestamp,omitempty"`   // When the error occurred
	Component   string         `json:"component,omitempty"`   // Which component/module generated the error
	Operation   string         `json:"operation,omitempty"`   // What operation was being performed
	UserID      string         `json:"user_id,omitempty"`     // User context if available
	RequestID   string         `json:"request_id,omitempty"`  // Request correlation ID
}

func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

func (e *AppError) Unwrap() error {
	return e.Cause
}

// newAppError is an unexported helper to create AppError instances
func newAppError(typ ErrorType, code, message string, cause error) *AppError {
	err := &AppError{
		Type:        typ,
		Code:        code,
		Message:     message,
		Cause:       cause,
		Severity:    getDefaultSeverity(typ),
		Timestamp:   time.Now(),
		Fingerprint: generateErrorFingerprint(typ, code, message),
	}
	return err
}

// newAppErrorWithSeverity creates an AppError with specified severity
func newAppErrorWithSeverity(typ ErrorType, code, message string, cause error, severity ErrorSeverity) *AppError {
	err := newAppError(typ, code, message, cause)
	err.Severity = severity
	err.Fingerprint = generateErrorFingerprint(typ, code, message) // Regenerate with severity
	return err
}

// getDefaultSeverity returns the default severity for an error type
func getDefaultSeverity(typ ErrorType) ErrorSeverity {
	switch typ {
	case ErrorTypeValidation:
		return ErrorSeverityMedium
	case ErrorTypeIO:
		return ErrorSeverityMedium
	case ErrorTypeAI:
		return ErrorSeverityHigh
	case ErrorTypeNetwork:
		return ErrorSeverityMedium
	case ErrorTypeConfig:
		return ErrorSeverityHigh
	case ErrorTypeInternal:
		return ErrorSeverityCritical
	default:
		return ErrorSeverityMedium
	}
}

// generateErrorFingerprint creates a unique fingerprint for error deduplication
func generateErrorFingerprint(typ ErrorType, code, message string) string {
	// Create a hash of type, code, and message for deduplication
	content := fmt.Sprintf("%s:%s:%s", typ, code, message)
	hash := sha256.Sum256([]byte(content))
	return fmt.Sprintf("%x", hash[:8]) // Use first 8 bytes for shorter fingerprint
}

// Error constructors for different types
func NewValidationError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeValidation, code, message, cause)
}

func NewValidationErrorWithSeverity(code, message string, cause error, severity ErrorSeverity) *AppError {
	return newAppErrorWithSeverity(ErrorTypeValidation, code, message, cause, severity)
}

func NewIOError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeIO, code, message, cause)
}

func NewIOErrorWithSeverity(code, message string, cause error, severity ErrorSeverity) *AppError {
	return newAppErrorWithSeverity(ErrorTypeIO, code, message, cause, severity)
}

func NewAIError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeAI, code, message, cause)
}

func NewAIErrorWithSeverity(code, message string, cause error, severity ErrorSeverity) *AppError {
	return newAppErrorWithSeverity(ErrorTypeAI, code, message, cause, severity)
}

func NewNetworkError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeNetwork, code, message, cause)
}

func NewNetworkErrorWithSeverity(code, message string, cause error, severity ErrorSeverity) *AppError {
	return newAppErrorWithSeverity(ErrorTypeNetwork, code, message, cause, severity)
}

func NewConfigError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeConfig, code, message, cause)
}

func NewConfigErrorWithSeverity(code, message string, cause error, severity ErrorSeverity) *AppError {
	return newAppErrorWithSeverity(ErrorTypeConfig, code, message, cause, severity)
}

func NewInternalError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeInternal, code, message, cause)
}

func NewInternalErrorWithSeverity(code, message string, cause error, severity ErrorSeverity) *AppError {
	return newAppErrorWithSeverity(ErrorTypeInternal, code, message, cause, severity)
}

// WithContext adds context to an error
func (e *AppError) WithContext(key string, value any) *AppError {
	if e.Context == nil {
		e.Context = make(map[string]any)
	}
	e.Context[key] = value
	return e
}

// WithSeverity sets the error severity
func (e *AppError) WithSeverity(severity ErrorSeverity) *AppError {
	e.Severity = severity
	return e
}

// WithComponent sets the component that generated the error
func (e *AppError) WithComponent(component string) *AppError {
	e.Component = component
	return e
}

// WithOperation sets the operation that was being performed
func (e *AppError) WithOperation(operation string) *AppError {
	e.Operation = operation
	return e
}

// WithUserID sets the user context
func (e *AppError) WithUserID(userID string) *AppError {
	e.UserID = userID
	return e
}

// WithRequestID sets the request correlation ID
func (e *AppError) WithRequestID(requestID string) *AppError {
	e.RequestID = requestID
	return e
}

// WithCorrelation sets both user and request IDs for full correlation
func (e *AppError) WithCorrelation(userID, requestID string) *AppError {
	e.UserID = userID
	e.RequestID = requestID
	return e
}

// GetStackTrace captures the current stack trace
func (e *AppError) GetStackTrace() []string {
	var stack []string
	pc := make([]uintptr, 10)   // Capture up to 10 frames
	n := runtime.Callers(2, pc) // Skip this function and the caller
	frames := runtime.CallersFrames(pc[:n])

	for {
		frame, more := frames.Next()
		if !strings.Contains(frame.File, "runtime/") {
			stack = append(stack, fmt.Sprintf("%s:%d %s", frame.File, frame.Line, frame.Function))
		}
		if !more {
			break
		}
	}
	return stack
}

// Logger wraps slog with application-specific methods and optional OpenTelemetry log bridge
type Logger struct {
	logger     *slog.Logger
	otelLogger log.Logger
	config     LoggerConfig
}

// LoggerConfig holds configuration for the logger bridge
type LoggerConfig struct {
	EnableOTelLogs     bool
	EnableTraceContext bool
	ServiceName        string
}

// NewLogger creates a new structured logger
func NewLogger(level slog.Level) *Logger {
	return NewLoggerWithConfig(level, LoggerConfig{
		EnableOTelLogs:     false, // Default to slog only for backward compatibility
		EnableTraceContext: false,
		ServiceName:        "resumatter",
	})
}

// NewLoggerWithConfig creates a new structured logger with OpenTelemetry bridge configuration
func NewLoggerWithConfig(level slog.Level, config LoggerConfig) *Logger {
	opts := &slog.HandlerOptions{
		Level: level,
	}

	handler := slog.NewJSONHandler(os.Stdout, opts)
	slogLogger := slog.New(handler)

	var otelLogger log.Logger
	if config.EnableOTelLogs {
		otelLogger = global.Logger(config.ServiceName)
	}

	return &Logger{
		logger:     slogLogger,
		otelLogger: otelLogger,
		config:     config,
	}
}

// emitOTelLog emits a log record to OpenTelemetry if enabled
func (l *Logger) emitOTelLog(ctx context.Context, severity log.Severity, message string, attrs []attribute.KeyValue) {
	if l.otelLogger == nil || !l.config.EnableOTelLogs {
		return
	}

	var record log.Record
	record.SetTimestamp(time.Now())
	record.SetSeverity(severity)
	record.SetBody(log.StringValue(message))

	// Add trace context if enabled and available
	if l.config.EnableTraceContext {
		if span := trace.SpanFromContext(ctx); span.SpanContext().IsValid() {
			spanCtx := span.SpanContext()
			attrs = append(attrs,
				attribute.String("trace_id", spanCtx.TraceID().String()),
				attribute.String("span_id", spanCtx.SpanID().String()),
			)
		}
	}

	// Convert attribute.KeyValue to log.KeyValue and add to record
	logAttrs := make([]log.KeyValue, len(attrs))
	for i, attr := range attrs {
		logAttrs[i] = log.KeyValueFromAttribute(attr)
	}
	record.AddAttributes(logAttrs...)

	l.otelLogger.Emit(ctx, record)
}

// emitErrorEvent emits a structured error event for enhanced observability
func (l *Logger) emitErrorEvent(ctx context.Context, appErr *AppError, message string, baseAttrs []attribute.KeyValue) {
	if l.otelLogger == nil || !l.config.EnableOTelLogs {
		return
	}

	// Create an error event record with enhanced attributes
	var record log.Record
	record.SetTimestamp(time.Now())
	record.SetSeverity(l.mapErrorSeverityToLogSeverity(appErr.Severity))

	// Create structured error event body
	eventBody := fmt.Sprintf("ERROR_EVENT: %s [%s:%s]", message, appErr.Type, appErr.Code)
	record.SetBody(log.StringValue(eventBody))

	// Enhanced error event attributes
	eventAttrs := []attribute.KeyValue{
		attribute.String("event.type", "error"),
		attribute.String("event.name", "application_error"),
		attribute.String("error.fingerprint", appErr.Fingerprint),
		attribute.String("error.severity", string(appErr.Severity)),
		attribute.String("error.type", string(appErr.Type)),
		attribute.String("error.code", appErr.Code),
		attribute.Bool("error.is_structured", true),
	}

	// Add timing information
	if !appErr.Timestamp.IsZero() {
		eventAttrs = append(eventAttrs,
			attribute.String("error.occurred_at", appErr.Timestamp.Format(time.RFC3339)),
			attribute.Int64("error.age_ms", time.Since(appErr.Timestamp).Milliseconds()),
		)
	}

	// Add correlation context
	if appErr.Component != "" || appErr.Operation != "" {
		eventAttrs = append(eventAttrs, attribute.String("error.context",
			fmt.Sprintf("%s:%s", appErr.Component, appErr.Operation)))
	}

	// Combine with base attributes
	eventAttrs = append(eventAttrs, baseAttrs...)

	// Add trace context if enabled and available
	if l.config.EnableTraceContext {
		if span := trace.SpanFromContext(ctx); span.SpanContext().IsValid() {
			spanCtx := span.SpanContext()
			eventAttrs = append(eventAttrs,
				attribute.String("trace_id", spanCtx.TraceID().String()),
				attribute.String("span_id", spanCtx.SpanID().String()),
			)
		}
	}

	// Convert to log attributes and emit
	logAttrs := make([]log.KeyValue, len(eventAttrs))
	for i, attr := range eventAttrs {
		logAttrs[i] = log.KeyValueFromAttribute(attr)
	}
	record.AddAttributes(logAttrs...)

	l.otelLogger.Emit(ctx, record)
}

// mapErrorSeverityToLogSeverity maps AppError severity to OpenTelemetry log severity
func (l *Logger) mapErrorSeverityToLogSeverity(severity ErrorSeverity) log.Severity {
	switch severity {
	case ErrorSeverityLow:
		return log.SeverityWarn
	case ErrorSeverityMedium:
		return log.SeverityError
	case ErrorSeverityHigh:
		return log.SeverityError
	case ErrorSeverityCritical:
		return log.SeverityFatal
	default:
		return log.SeverityError
	}
}

// convertArgsToAttributes converts slog-style args to OpenTelemetry attributes
func (l *Logger) convertArgsToAttributes(args []any) []attribute.KeyValue {
	var attrs []attribute.KeyValue

	// Process args in pairs (key, value)
	for i := 0; i < len(args)-1; i += 2 {
		if key, ok := args[i].(string); ok {
			value := args[i+1]
			attrs = append(attrs, convertValueToAttribute(key, value))
		}
	}

	return attrs
}

// convertValueToAttribute converts a value to an OpenTelemetry attribute
func convertValueToAttribute(key string, value any) attribute.KeyValue {
	switch v := value.(type) {
	case string:
		return attribute.String(key, v)
	case int:
		return attribute.Int(key, v)
	case int64:
		return attribute.Int64(key, v)
	case float64:
		return attribute.Float64(key, v)
	case bool:
		return attribute.Bool(key, v)
	case ErrorType:
		return attribute.String(key, string(v))
	default:
		// Convert to string for unsupported types
		return attribute.String(key, fmt.Sprintf("%v", v))
	}
}

// LogError logs an application error with appropriate level and context
func (l *Logger) LogError(err error, message string, args ...any) {
	l.LogErrorWithContext(context.Background(), err, message, args...)
}

// LogErrorWithContext logs an application error with context for trace correlation
func (l *Logger) LogErrorWithContext(ctx context.Context, err error, message string, args ...any) {
	var logArgs []any
	var otelAttrs []attribute.KeyValue

	if appErr, ok := err.(*AppError); ok {
		// Enhanced slog attributes
		logArgs = []any{
			"error_type", appErr.Type,
			"error_code", appErr.Code,
			"error_message", appErr.Message,
			"error_severity", appErr.Severity,
			"error_fingerprint", appErr.Fingerprint,
		}

		// Enhanced OpenTelemetry attributes
		otelAttrs = []attribute.KeyValue{
			attribute.String("error.type", string(appErr.Type)),
			attribute.String("error.code", appErr.Code),
			attribute.String("error.message", appErr.Message),
			attribute.String("error.severity", string(appErr.Severity)),
			attribute.String("error.fingerprint", appErr.Fingerprint),
		}

		// Add timestamp if available
		if !appErr.Timestamp.IsZero() {
			logArgs = append(logArgs, "error_timestamp", appErr.Timestamp.Format(time.RFC3339))
			otelAttrs = append(otelAttrs, attribute.String("error.timestamp", appErr.Timestamp.Format(time.RFC3339)))
		}

		// Add component and operation context
		if appErr.Component != "" {
			logArgs = append(logArgs, "error_component", appErr.Component)
			otelAttrs = append(otelAttrs, attribute.String("error.component", appErr.Component))
		}
		if appErr.Operation != "" {
			logArgs = append(logArgs, "error_operation", appErr.Operation)
			otelAttrs = append(otelAttrs, attribute.String("error.operation", appErr.Operation))
		}

		// Add correlation IDs
		if appErr.UserID != "" {
			logArgs = append(logArgs, "user_id", appErr.UserID)
			otelAttrs = append(otelAttrs, attribute.String("user.id", appErr.UserID))
		}
		if appErr.RequestID != "" {
			logArgs = append(logArgs, "request_id", appErr.RequestID)
			otelAttrs = append(otelAttrs, attribute.String("request.id", appErr.RequestID))
		}

		// Add stack trace for critical errors
		if appErr.Severity == ErrorSeverityCritical {
			stackTrace := appErr.GetStackTrace()
			if len(stackTrace) > 0 {
				logArgs = append(logArgs, "stack_trace", strings.Join(stackTrace, "\n"))
				otelAttrs = append(otelAttrs, attribute.String("error.stack_trace", strings.Join(stackTrace, "\n")))
			}
		}

		// Add context if available
		for key, value := range appErr.Context {
			logArgs = append(logArgs, key, value)
			otelAttrs = append(otelAttrs, convertValueToAttribute(key, value))
		}

		// Add additional args
		logArgs = append(logArgs, args...)
		otelAttrs = append(otelAttrs, l.convertArgsToAttributes(args)...)

		// Add the underlying error if present
		if appErr.Cause != nil {
			logArgs = append(logArgs, "error_cause", appErr.Cause.Error())
			otelAttrs = append(otelAttrs, attribute.String("error.cause", appErr.Cause.Error()))
		}

		// Emit error event for observability
		l.emitErrorEvent(ctx, appErr, message, otelAttrs)
	} else {
		// Regular error
		logArgs = append([]any{"error", err.Error()}, args...)
		otelAttrs = []attribute.KeyValue{
			attribute.String("error", err.Error()),
			attribute.String("error.type", "generic"),
			attribute.String("error.severity", string(ErrorSeverityMedium)),
		}
		otelAttrs = append(otelAttrs, l.convertArgsToAttributes(args)...)
	}

	// Log to slog (existing behavior)
	l.logger.Error(message, logArgs...)

	// Log to OpenTelemetry if enabled
	l.emitOTelLog(ctx, log.SeverityError, message, otelAttrs)
}

func (l *Logger) Info(message string, args ...any) {
	l.InfoWithContext(context.Background(), message, args...)
}

func (l *Logger) InfoWithContext(ctx context.Context, message string, args ...any) {
	// Log to slog (existing behavior)
	l.logger.Info(message, args...)

	// Log to OpenTelemetry if enabled
	otelAttrs := l.convertArgsToAttributes(args)
	l.emitOTelLog(ctx, log.SeverityInfo, message, otelAttrs)
}

func (l *Logger) Debug(message string, args ...any) {
	l.DebugWithContext(context.Background(), message, args...)
}

func (l *Logger) DebugWithContext(ctx context.Context, message string, args ...any) {
	// Log to slog (existing behavior)
	l.logger.Debug(message, args...)

	// Log to OpenTelemetry if enabled
	otelAttrs := l.convertArgsToAttributes(args)
	l.emitOTelLog(ctx, log.SeverityDebug, message, otelAttrs)
}

func (l *Logger) Warn(message string, args ...any) {
	l.WarnWithContext(context.Background(), message, args...)
}

func (l *Logger) WarnWithContext(ctx context.Context, message string, args ...any) {
	// Log to slog (existing behavior)
	l.logger.Warn(message, args...)

	// Log to OpenTelemetry if enabled
	otelAttrs := l.convertArgsToAttributes(args)
	l.emitOTelLog(ctx, log.SeverityWarn, message, otelAttrs)
}

// New creates a new logger instance
func New(level string) (*Logger, error) {
	return NewWithConfig(level, LoggerConfig{
		EnableOTelLogs:     false, // Default to slog only for backward compatibility
		EnableTraceContext: false,
		ServiceName:        "resumatter",
	})
}

// NewWithConfig creates a new logger instance with OpenTelemetry bridge configuration
func NewWithConfig(level string, config LoggerConfig) (*Logger, error) {
	var slogLevel slog.Level
	switch level {
	case "debug":
		slogLevel = slog.LevelDebug
	case "info":
		slogLevel = slog.LevelInfo
	case "warn":
		slogLevel = slog.LevelWarn
	case "error":
		slogLevel = slog.LevelError
	default:
		return nil, fmt.Errorf("invalid log level: %s", level)
	}

	return NewLoggerWithConfig(slogLevel, config), nil
}

// NewLoggerFromObservabilityConfig creates a logger from observability configuration
// This function bridges the gap between the observability config and the logger
func NewLoggerFromObservabilityConfig(level string, serviceName string, logsEnabled bool, traceContextEnabled bool) (*Logger, error) {
	config := LoggerConfig{
		EnableOTelLogs:     logsEnabled,
		EnableTraceContext: traceContextEnabled,
		ServiceName:        serviceName,
	}

	return NewWithConfig(level, config)
}

// NewStructuredError creates a fully structured AppError with all context
func NewStructuredError(typ ErrorType, code, message string, cause error, severity ErrorSeverity, component, operation string) *AppError {
	err := newAppErrorWithSeverity(typ, code, message, cause, severity)
	err.Component = component
	err.Operation = operation
	return err
}

// NewErrorWithCorrelation creates an AppError with correlation context
func NewErrorWithCorrelation(typ ErrorType, code, message string, cause error, userID, requestID string) *AppError {
	err := newAppError(typ, code, message, cause)
	err.UserID = userID
	err.RequestID = requestID
	return err
}

// LogStructuredError is a convenience method for logging errors with full context
func (l *Logger) LogStructuredError(ctx context.Context, typ ErrorType, code, message, logMessage string, severity ErrorSeverity, component, operation string, args ...any) {
	err := NewStructuredError(typ, code, message, nil, severity, component, operation)
	l.LogErrorWithContext(ctx, err, logMessage, args...)
}

// LogErrorWithMetrics logs an error and optionally increments error metrics
func (l *Logger) LogErrorWithMetrics(ctx context.Context, err error, message string, incrementMetrics bool, args ...any) {
	l.LogErrorWithContext(ctx, err, message, args...)

	// TODO: Add metrics integration when metrics are available in context
	// This would increment error counters by type, severity, component, etc.
	if incrementMetrics {
		// Placeholder for future metrics integration
		// metrics.IncrementErrorCounter(err.Type, err.Severity, err.Component)
	}
}

// Common error codes
const (
	ErrCodeFileNotFound    = "FILE_NOT_FOUND"
	ErrCodeFileNotReadable = "FILE_NOT_READABLE"
	ErrCodeInvalidFormat   = "INVALID_FORMAT"
	ErrCodeAIServiceFailed = "AI_SERVICE_FAILED"
	ErrCodeAITimeout       = "AI_TIMEOUT"
	ErrCodeInvalidRequest  = "INVALID_REQUEST"
	ErrCodeMissingAPIKey   = "MISSING_API_KEY"
	ErrCodeNetworkTimeout  = "NETWORK_TIMEOUT"
	ErrCodeInvalidConfig   = "INVALID_CONFIG"
)
