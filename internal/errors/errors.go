package errors

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/log"
	"go.opentelemetry.io/otel/log/global"
	"go.opentelemetry.io/otel/trace"
)

// ErrorType represents different categories of errors
type ErrorType string

const (
	ErrorTypeValidation ErrorType = "validation"
	ErrorTypeIO         ErrorType = "io"
	ErrorTypeAI         ErrorType = "ai"
	ErrorTypeNetwork    ErrorType = "network"
	ErrorTypeConfig     ErrorType = "config"
	ErrorTypeInternal   ErrorType = "internal"
)

// AppError represents a structured application error
type AppError struct {
	Type    ErrorType      `json:"type"`
	Code    string         `json:"code"`
	Message string         `json:"message"`
	Cause   error          `json:"cause,omitempty"`
	Context map[string]any `json:"context,omitempty"`
}

func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

func (e *AppError) Unwrap() error {
	return e.Cause
}

// newAppError is an unexported helper to create AppError instances
func newAppError(typ ErrorType, code, message string, cause error) *AppError {
	return &AppError{
		Type:    typ,
		Code:    code,
		Message: message,
		Cause:   cause,
	}
}

// Error constructors for different types
func NewValidationError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeValidation, code, message, cause)
}

func NewIOError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeIO, code, message, cause)
}

func NewAIError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeAI, code, message, cause)
}

func NewNetworkError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeNetwork, code, message, cause)
}

func NewConfigError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeConfig, code, message, cause)
}

func NewInternalError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeInternal, code, message, cause)
}

// WithContext adds context to an error
func (e *AppError) WithContext(key string, value any) *AppError {
	if e.Context == nil {
		e.Context = make(map[string]any)
	}
	e.Context[key] = value
	return e
}

// Logger wraps slog with application-specific methods and optional OpenTelemetry log bridge
type Logger struct {
	logger     *slog.Logger
	otelLogger log.Logger
	config     LoggerConfig
}

// LoggerConfig holds configuration for the logger bridge
type LoggerConfig struct {
	EnableOTelLogs     bool
	EnableTraceContext bool
	ServiceName        string
}

// NewLogger creates a new structured logger
func NewLogger(level slog.Level) *Logger {
	return NewLoggerWithConfig(level, LoggerConfig{
		EnableOTelLogs:     false, // Default to slog only for backward compatibility
		EnableTraceContext: false,
		ServiceName:        "resumatter",
	})
}

// NewLoggerWithConfig creates a new structured logger with OpenTelemetry bridge configuration
func NewLoggerWithConfig(level slog.Level, config LoggerConfig) *Logger {
	opts := &slog.HandlerOptions{
		Level: level,
	}

	handler := slog.NewJSONHandler(os.Stdout, opts)
	slogLogger := slog.New(handler)

	var otelLogger log.Logger
	if config.EnableOTelLogs {
		otelLogger = global.Logger(config.ServiceName)
	}

	return &Logger{
		logger:     slogLogger,
		otelLogger: otelLogger,
		config:     config,
	}
}

// emitOTelLog emits a log record to OpenTelemetry if enabled
func (l *Logger) emitOTelLog(ctx context.Context, severity log.Severity, message string, attrs []attribute.KeyValue) {
	if l.otelLogger == nil || !l.config.EnableOTelLogs {
		return
	}

	var record log.Record
	record.SetTimestamp(time.Now())
	record.SetSeverity(severity)
	record.SetBody(log.StringValue(message))

	// Add trace context if enabled and available
	if l.config.EnableTraceContext {
		if span := trace.SpanFromContext(ctx); span.SpanContext().IsValid() {
			spanCtx := span.SpanContext()
			attrs = append(attrs,
				attribute.String("trace_id", spanCtx.TraceID().String()),
				attribute.String("span_id", spanCtx.SpanID().String()),
			)
		}
	}

	// Convert attribute.KeyValue to log.KeyValue and add to record
	logAttrs := make([]log.KeyValue, len(attrs))
	for i, attr := range attrs {
		logAttrs[i] = log.KeyValueFromAttribute(attr)
	}
	record.AddAttributes(logAttrs...)

	l.otelLogger.Emit(ctx, record)
}

// convertArgsToAttributes converts slog-style args to OpenTelemetry attributes
func (l *Logger) convertArgsToAttributes(args []any) []attribute.KeyValue {
	var attrs []attribute.KeyValue

	// Process args in pairs (key, value)
	for i := 0; i < len(args)-1; i += 2 {
		if key, ok := args[i].(string); ok {
			value := args[i+1]
			attrs = append(attrs, convertValueToAttribute(key, value))
		}
	}

	return attrs
}

// convertValueToAttribute converts a value to an OpenTelemetry attribute
func convertValueToAttribute(key string, value any) attribute.KeyValue {
	switch v := value.(type) {
	case string:
		return attribute.String(key, v)
	case int:
		return attribute.Int(key, v)
	case int64:
		return attribute.Int64(key, v)
	case float64:
		return attribute.Float64(key, v)
	case bool:
		return attribute.Bool(key, v)
	case ErrorType:
		return attribute.String(key, string(v))
	default:
		// Convert to string for unsupported types
		return attribute.String(key, fmt.Sprintf("%v", v))
	}
}

// LogError logs an application error with appropriate level and context
func (l *Logger) LogError(err error, message string, args ...any) {
	l.LogErrorWithContext(context.Background(), err, message, args...)
}

// LogErrorWithContext logs an application error with context for trace correlation
func (l *Logger) LogErrorWithContext(ctx context.Context, err error, message string, args ...any) {
	var logArgs []any
	var otelAttrs []attribute.KeyValue

	if appErr, ok := err.(*AppError); ok {
		logArgs = []any{
			"error_type", appErr.Type,
			"error_code", appErr.Code,
			"error_message", appErr.Message,
		}

		// Build OpenTelemetry attributes for structured error
		otelAttrs = []attribute.KeyValue{
			attribute.String("error.type", string(appErr.Type)),
			attribute.String("error.code", appErr.Code),
			attribute.String("error.message", appErr.Message),
		}

		// Add context if available
		for key, value := range appErr.Context {
			logArgs = append(logArgs, key, value)
			otelAttrs = append(otelAttrs, convertValueToAttribute(key, value))
		}

		// Add additional args
		logArgs = append(logArgs, args...)
		otelAttrs = append(otelAttrs, l.convertArgsToAttributes(args)...)

		// Add the underlying error if present
		if appErr.Cause != nil {
			otelAttrs = append(otelAttrs, attribute.String("error.cause", appErr.Cause.Error()))
		}
	} else {
		// Regular error
		logArgs = append([]any{"error", err.Error()}, args...)
		otelAttrs = []attribute.KeyValue{
			attribute.String("error", err.Error()),
		}
		otelAttrs = append(otelAttrs, l.convertArgsToAttributes(args)...)
	}

	// Log to slog (existing behavior)
	l.logger.Error(message, logArgs...)

	// Log to OpenTelemetry if enabled
	l.emitOTelLog(ctx, log.SeverityError, message, otelAttrs)
}

func (l *Logger) Info(message string, args ...any) {
	l.InfoWithContext(context.Background(), message, args...)
}

func (l *Logger) InfoWithContext(ctx context.Context, message string, args ...any) {
	// Log to slog (existing behavior)
	l.logger.Info(message, args...)

	// Log to OpenTelemetry if enabled
	otelAttrs := l.convertArgsToAttributes(args)
	l.emitOTelLog(ctx, log.SeverityInfo, message, otelAttrs)
}

func (l *Logger) Debug(message string, args ...any) {
	l.DebugWithContext(context.Background(), message, args...)
}

func (l *Logger) DebugWithContext(ctx context.Context, message string, args ...any) {
	// Log to slog (existing behavior)
	l.logger.Debug(message, args...)

	// Log to OpenTelemetry if enabled
	otelAttrs := l.convertArgsToAttributes(args)
	l.emitOTelLog(ctx, log.SeverityDebug, message, otelAttrs)
}

func (l *Logger) Warn(message string, args ...any) {
	l.WarnWithContext(context.Background(), message, args...)
}

func (l *Logger) WarnWithContext(ctx context.Context, message string, args ...any) {
	// Log to slog (existing behavior)
	l.logger.Warn(message, args...)

	// Log to OpenTelemetry if enabled
	otelAttrs := l.convertArgsToAttributes(args)
	l.emitOTelLog(ctx, log.SeverityWarn, message, otelAttrs)
}

// New creates a new logger instance
func New(level string) (*Logger, error) {
	return NewWithConfig(level, LoggerConfig{
		EnableOTelLogs:     false, // Default to slog only for backward compatibility
		EnableTraceContext: false,
		ServiceName:        "resumatter",
	})
}

// NewWithConfig creates a new logger instance with OpenTelemetry bridge configuration
func NewWithConfig(level string, config LoggerConfig) (*Logger, error) {
	var slogLevel slog.Level
	switch level {
	case "debug":
		slogLevel = slog.LevelDebug
	case "info":
		slogLevel = slog.LevelInfo
	case "warn":
		slogLevel = slog.LevelWarn
	case "error":
		slogLevel = slog.LevelError
	default:
		return nil, fmt.Errorf("invalid log level: %s", level)
	}

	return NewLoggerWithConfig(slogLevel, config), nil
}

// NewLoggerFromObservabilityConfig creates a logger from observability configuration
// This function bridges the gap between the observability config and the logger
func NewLoggerFromObservabilityConfig(level string, serviceName string, logsEnabled bool, traceContextEnabled bool) (*Logger, error) {
	config := LoggerConfig{
		EnableOTelLogs:     logsEnabled,
		EnableTraceContext: traceContextEnabled,
		ServiceName:        serviceName,
	}

	return NewWithConfig(level, config)
}

// Common error codes
const (
	ErrCodeFileNotFound    = "FILE_NOT_FOUND"
	ErrCodeFileNotReadable = "FILE_NOT_READABLE"
	ErrCodeInvalidFormat   = "INVALID_FORMAT"
	ErrCodeAIServiceFailed = "AI_SERVICE_FAILED"
	ErrCodeAITimeout       = "AI_TIMEOUT"
	ErrCodeInvalidRequest  = "INVALID_REQUEST"
	ErrCodeMissingAPIKey   = "MISSING_API_KEY"
	ErrCodeNetworkTimeout  = "NETWORK_TIMEOUT"
	ErrCodeInvalidConfig   = "INVALID_CONFIG"
)
