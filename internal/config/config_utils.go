package config

import (
	"fmt"
	"log/slog"
	"os"
	"strings"
)

// applyFallbacks applies environment variable fallbacks
func (c *Config) applyFallbacks() {
	// Note: API key fallbacks are now handled in Get...Config() methods to avoid duplication

	c.applyServerAPIKeyFallbacks()
	c.applyTLSDefaults()
	c.applyObservabilityDefaults()
}

// applyServerAPIKeyFallbacks applies API key fallbacks from environment variables
func (c *Config) applyServerAPIKeyFallbacks() {
	if len(c.Server.APIKeys) == 0 {
		if apiKeysEnv := os.Getenv("RESUMATTER_SERVER_APIKEYS"); apiKeysEnv != "" {
			c.Server.APIKeys = strings.Split(apiKeysEnv, ",")
			// Trim whitespace from each key
			for i, key := range c.Server.APIKeys {
				c.Server.APIKeys[i] = strings.TrimSpace(key)
			}
		}
	}
}

// applyTLSDefaults applies default TLS configuration values
func (c *Config) applyTLSDefaults() {
	// Set default client auth policy for mutual TLS if not specified
	if c.Server.TLS.Mode == "mutual" && c.Server.TLS.ClientAuthPolicy == "" {
		c.Server.TLS.ClientAuthPolicy = "require"
	}

	// Set default TLS version if not specified
	if c.Server.TLS.MinVersion == "" && c.Server.TLS.Mode != "disabled" {
		c.Server.TLS.MinVersion = "1.2"
	}
}

// applyObservabilityDefaults applies default observability configuration values
func (c *Config) applyObservabilityDefaults() {
	if c.Observability.ServiceInstance == "" {
		c.Observability.ServiceInstance = generateServiceInstanceID(c.Observability.ServiceName)
	}
}

// generateServiceInstanceID generates a unique service instance ID
func generateServiceInstanceID(serviceName string) string {
	// Try to get hostname, fallback to default
	if hostname, err := os.Hostname(); err == nil {
		return fmt.Sprintf("%s-%s", serviceName, hostname)
	}
	return fmt.Sprintf("%s-1", serviceName)
}

// logConfigurationSources logs a summary of configuration sources being used
func (c *Config) logConfigurationSources(configFileUsed string, logger *slog.Logger) {
	logger.Info("=== Configuration Sources Summary ===")

	// Log config file source
	if configFileUsed != "" {
		logger.Info("Config file source", "path", configFileUsed)
	} else {
		logger.Info("Config file source", "status", "None (using defaults)")
	}

	// Log environment variables that are set
	envVars := []string{
		"RESUMATTER_AI_APIKEY",
		"RESUMATTER_AI_PROVIDER",
		"RESUMATTER_AI_MODEL",
		"RESUMATTER_SERVER_PORT",
		"RESUMATTER_SERVER_HOST",
		"RESUMATTER_APP_LOGLEVEL",
		"RESUMATTER_VAULT_ENABLED",
		"GEMINI_API_KEY", // Legacy support
	}

	envVarValues := make(map[string]string)
	hasEnvVars := false
	for _, envVar := range envVars {
		if value := os.Getenv(envVar); value != "" {
			// Mask sensitive values
			if strings.Contains(strings.ToLower(envVar), "apikey") || strings.Contains(strings.ToLower(envVar), "key") {
				envVarValues[envVar] = "***MASKED***"
			} else {
				envVarValues[envVar] = value
			}
			hasEnvVars = true
		}
	}

	if hasEnvVars {
		logger.Info("Environment variables set", "variables", envVarValues)
	} else {
		logger.Info("Environment variables set", "status", "None")
	}

	// Log key configuration values (with sensitive data masked)
	logger.Info("=== Key Configuration Values ===")

	apiKeyStatus := "***NOT SET***"
	if c.AI.APIKey != "" {
		apiKeyStatus = "***CONFIGURED***"
	}

	logger.Info("AI configuration",
		"provider", c.AI.Provider,
		"model", c.AI.Model,
		"api_key_status", apiKeyStatus)

	logger.Info("Server configuration",
		"host", c.Server.Host,
		"port", c.Server.Port,
		"tls_mode", c.Server.TLS.Mode)

	logger.Info("Application configuration",
		"log_level", c.App.LogLevel,
		"vault_enabled", c.Vault.Enabled,
		"observability_enabled", c.Observability.Enabled)

	// Log operation-specific configurations
	logger.Info("=== Operation-Specific AI Configurations ===")
	logger.Info("Operation AI configs",
		"tailor_provider", c.AI.Tailor.Provider,
		"tailor_model", c.AI.Tailor.Model,
		"evaluate_provider", c.AI.Evaluate.Provider,
		"evaluate_model", c.AI.Evaluate.Model,
		"analyze_provider", c.AI.Analyze.Provider,
		"analyze_model", c.AI.Analyze.Model)

	logger.Info("=====================================")
}
