package config

import (
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
)

// PromptSource represents where a prompt was loaded from
type PromptSource struct {
	Source    string // "file", "operation-config", "global-config", or "default"
	FilePath  string // Set if Source is "file"
	Operation string // The operation this prompt is for
	Type      string // "system" or "user"
}

// GetLoadedPrompts returns the loaded prompt content in a thread-safe way
func GetLoadedPrompts() *AllLoadedPrompts {
	return &loadedPrompts
}

// trackPromptSource tracks the source of a prompt for debugging
func (c *Config) trackPromptSource(source PromptSource) {
	// Prompt source tracking can be implemented when new logging is hooked up
}

// loadPromptsFromFiles loads custom prompts from external files if file paths are specified
func (c *Config) loadPromptsFromFiles(logger *slog.Logger) error {
	logger.Info("Starting custom prompt loading from files")

	// Initialize loaded prompts exactly once
	loadedPromptsOnce.Do(func() {
		loadedPrompts = AllLoadedPrompts{}
	})

	// Load global prompts
	if err := c.loadSystemPromptsFromFiles(&c.AI.CustomPrompts.SystemPrompts, &loadedPrompts.Global.SystemPrompts, logger); err != nil {
		return fmt.Errorf("failed to load global system prompts: %w", err)
	}
	if err := c.loadUserPromptsFromFiles(&c.AI.CustomPrompts.UserPrompts, &loadedPrompts.Global.UserPrompts, logger); err != nil {
		return fmt.Errorf("failed to load global user prompts: %w", err)
	}

	// Load operation-specific prompts
	if err := c.loadSystemPromptsFromFiles(&c.AI.Tailor.CustomPrompts.SystemPrompts, &loadedPrompts.Tailor.SystemPrompts, logger); err != nil {
		return fmt.Errorf("failed to load tailor system prompts: %w", err)
	}
	if err := c.loadUserPromptsFromFiles(&c.AI.Tailor.CustomPrompts.UserPrompts, &loadedPrompts.Tailor.UserPrompts, logger); err != nil {
		return fmt.Errorf("failed to load tailor user prompts: %w", err)
	}

	if err := c.loadSystemPromptsFromFiles(&c.AI.Evaluate.CustomPrompts.SystemPrompts, &loadedPrompts.Evaluate.SystemPrompts, logger); err != nil {
		return fmt.Errorf("failed to load evaluate system prompts: %w", err)
	}
	if err := c.loadUserPromptsFromFiles(&c.AI.Evaluate.CustomPrompts.UserPrompts, &loadedPrompts.Evaluate.UserPrompts, logger); err != nil {
		return fmt.Errorf("failed to load evaluate user prompts: %w", err)
	}

	if err := c.loadSystemPromptsFromFiles(&c.AI.Analyze.CustomPrompts.SystemPrompts, &loadedPrompts.Analyze.SystemPrompts, logger); err != nil {
		return fmt.Errorf("failed to load analyze system prompts: %w", err)
	}
	if err := c.loadUserPromptsFromFiles(&c.AI.Analyze.CustomPrompts.UserPrompts, &loadedPrompts.Analyze.UserPrompts, logger); err != nil {
		return fmt.Errorf("failed to load analyze user prompts: %w", err)
	}

	// Log summary of prompt sources after loading
	c.logPromptLoadingSummary(logger)

	return nil
}

// loadSystemPromptsFromFiles loads system prompts from files if file paths are specified
func (c *Config) loadSystemPromptsFromFiles(prompts *SystemPrompts, target *LoadedSystemPrompts, logger *slog.Logger) error {
	// Load TailorResume prompt from file if specified
	if prompts.TailorResumeFile != "" {
		content, err := c.loadPromptFromFile(prompts.TailorResumeFile, "system", "tailorResume", logger)
		if err != nil {
			return err
		}
		target.TailorResume = content
	}

	// Load EvaluateResume prompt from file if specified
	if prompts.EvaluateResumeFile != "" {
		content, err := c.loadPromptFromFile(prompts.EvaluateResumeFile, "system", "evaluateResume", logger)
		if err != nil {
			return err
		}
		target.EvaluateResume = content
	}

	// Load AnalyzeJob prompt from file if specified
	if prompts.AnalyzeJobFile != "" {
		content, err := c.loadPromptFromFile(prompts.AnalyzeJobFile, "system", "analyzeJob", logger)
		if err != nil {
			return err
		}
		target.AnalyzeJob = content
	}

	return nil
}

// loadUserPromptsFromFiles loads user prompts from files if file paths are specified
func (c *Config) loadUserPromptsFromFiles(prompts *UserPrompts, target *LoadedUserPrompts, logger *slog.Logger) error {
	// Load TailorResume prompt from file if specified
	if prompts.TailorResumeFile != "" {
		content, err := c.loadPromptFromFile(prompts.TailorResumeFile, "user", "tailorResume", logger)
		if err != nil {
			return err
		}
		target.TailorResume = content
	}

	// Load EvaluateResume prompt from file if specified
	if prompts.EvaluateResumeFile != "" {
		content, err := c.loadPromptFromFile(prompts.EvaluateResumeFile, "user", "evaluateResume", logger)
		if err != nil {
			return err
		}
		target.EvaluateResume = content
	}

	// Load AnalyzeJob prompt from file if specified
	if prompts.AnalyzeJobFile != "" {
		content, err := c.loadPromptFromFile(prompts.AnalyzeJobFile, "user", "analyzeJob", logger)
		if err != nil {
			return err
		}
		target.AnalyzeJob = content
	}

	return nil
}

// loadPromptFromFile loads a prompt from a file with proper error handling and logging
func (c *Config) loadPromptFromFile(filePath, promptType, operation string, logger *slog.Logger) (string, error) {
	// Track prompt source
	c.trackPromptSource(PromptSource{
		Source:    "file",
		FilePath:  filePath,
		Operation: operation,
		Type:      promptType,
	})

	// Resolve relative paths
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to resolve absolute path for %s %s prompt file '%s': %w", promptType, operation, filePath, err)
	}

	// Check if file exists
	if _, err := os.Stat(absPath); os.IsNotExist(err) {
		return "", fmt.Errorf("%s %s prompt file not found: %s", promptType, operation, absPath)
	}

	// Read file content
	content, err := os.ReadFile(absPath)
	if err != nil {
		return "", fmt.Errorf("failed to read %s %s prompt file '%s': %w", promptType, operation, absPath, err)
	}

	// Validate content is not empty
	trimmedContent := strings.TrimSpace(string(content))
	if trimmedContent == "" {
		return "", fmt.Errorf("%s %s prompt file '%s' is empty", promptType, operation, absPath)
	}

	// Log successful loading
	logger.Info("Successfully loaded prompt from file",
		"type", promptType,
		"operation", operation,
		"path", absPath,
		"characters", len(trimmedContent))

	return trimmedContent, nil
}

// validatePromptFiles validates that prompt files exist and are readable before loading
func (c *Config) validatePromptFiles(logger *slog.Logger) error {
	var validationErrors []string

	// Helper function to validate a file path
	validateFile := func(filePath, promptType, operation string) {
		if filePath == "" {
			return // No file specified, skip validation
		}

		absPath, err := filepath.Abs(filePath)
		if err != nil {
			validationErrors = append(validationErrors, fmt.Sprintf("invalid path for %s %s prompt: %s", promptType, operation, filePath))
			return
		}

		if _, err := os.Stat(absPath); os.IsNotExist(err) {
			validationErrors = append(validationErrors, fmt.Sprintf("%s %s prompt file not found: %s", promptType, operation, absPath))
		}
	}

	// Validate global prompt files
	validateFile(c.AI.CustomPrompts.SystemPrompts.TailorResumeFile, "system", "tailorResume")
	validateFile(c.AI.CustomPrompts.SystemPrompts.EvaluateResumeFile, "system", "evaluateResume")
	validateFile(c.AI.CustomPrompts.SystemPrompts.AnalyzeJobFile, "system", "analyzeJob")
	validateFile(c.AI.CustomPrompts.UserPrompts.TailorResumeFile, "user", "tailorResume")
	validateFile(c.AI.CustomPrompts.UserPrompts.EvaluateResumeFile, "user", "evaluateResume")
	validateFile(c.AI.CustomPrompts.UserPrompts.AnalyzeJobFile, "user", "analyzeJob")

	// Validate operation-specific prompt files
	validateFile(c.AI.Tailor.CustomPrompts.SystemPrompts.TailorResumeFile, "tailor system", "tailorResume")
	validateFile(c.AI.Tailor.CustomPrompts.UserPrompts.TailorResumeFile, "tailor user", "tailorResume")
	validateFile(c.AI.Evaluate.CustomPrompts.SystemPrompts.EvaluateResumeFile, "evaluate system", "evaluateResume")
	validateFile(c.AI.Evaluate.CustomPrompts.UserPrompts.EvaluateResumeFile, "evaluate user", "evaluateResume")
	validateFile(c.AI.Analyze.CustomPrompts.SystemPrompts.AnalyzeJobFile, "analyze system", "analyzeJob")
	validateFile(c.AI.Analyze.CustomPrompts.UserPrompts.AnalyzeJobFile, "analyze user", "analyzeJob")

	if len(validationErrors) > 0 {
		return fmt.Errorf("prompt file validation failed:\n%s", strings.Join(validationErrors, "\n"))
	}

	return nil
}

// logPromptLoadingSummary logs a summary of loaded prompts
func (c *Config) logPromptLoadingSummary(logger *slog.Logger) {
	logger.Info("=== Custom Prompt Loading Summary ===")

	promptCount := c.countAndLogLoadedPrompts(logger)

	c.logPromptSummaryFooter(promptCount, logger)
}

// countAndLogLoadedPrompts counts and logs all loaded prompts, returning the total count
func (c *Config) countAndLogLoadedPrompts(logger *slog.Logger) int {
	promptCount := 0

	// Check global prompts
	promptCount += c.logGlobalPrompts(logger)

	// Check operation-specific prompts
	promptCount += c.logOperationSpecificPrompts(logger)

	return promptCount
}

// logGlobalPrompts logs global prompt status and returns count
func (c *Config) logGlobalPrompts(logger *slog.Logger) int {
	count := 0

	promptChecks := []struct {
		content    string
		operation  string
		promptType string
	}{
		{loadedPrompts.Global.SystemPrompts.TailorResume, "tailor", "system"},
		{loadedPrompts.Global.SystemPrompts.EvaluateResume, "evaluate", "system"},
		{loadedPrompts.Global.SystemPrompts.AnalyzeJob, "analyze", "system"},
		{loadedPrompts.Global.UserPrompts.TailorResume, "tailor", "user"},
		{loadedPrompts.Global.UserPrompts.EvaluateResume, "evaluate", "user"},
		{loadedPrompts.Global.UserPrompts.AnalyzeJob, "analyze", "user"},
	}

	for _, check := range promptChecks {
		if check.content != "" {
			logger.Info("Global prompt loaded",
				"scope", "global",
				"type", check.promptType,
				"operation", check.operation,
				"source", "config/file")
			count++
		}
	}

	return count
}

// logOperationSpecificPrompts logs operation-specific prompt status and returns count
func (c *Config) logOperationSpecificPrompts(logger *slog.Logger) int {
	count := 0

	promptChecks := []struct {
		content    string
		operation  string
		promptType string
	}{
		{loadedPrompts.Tailor.SystemPrompts.TailorResume, "tailor", "system"},
		{loadedPrompts.Tailor.UserPrompts.TailorResume, "tailor", "user"},
		{loadedPrompts.Evaluate.SystemPrompts.EvaluateResume, "evaluate", "system"},
		{loadedPrompts.Evaluate.UserPrompts.EvaluateResume, "evaluate", "user"},
		{loadedPrompts.Analyze.SystemPrompts.AnalyzeJob, "analyze", "system"},
		{loadedPrompts.Analyze.UserPrompts.AnalyzeJob, "analyze", "user"},
	}

	for _, check := range promptChecks {
		if check.content != "" {
			logger.Info("Operation-specific prompt loaded",
				"scope", "operation-specific",
				"type", check.promptType,
				"operation", check.operation,
				"source", "config/file")
			count++
		}
	}

	return count
}

// logPromptSummaryFooter logs the summary footer with total count
func (c *Config) logPromptSummaryFooter(promptCount int, logger *slog.Logger) {
	if promptCount == 0 {
		logger.Info("No custom prompts loaded - using built-in defaults")
	} else {
		logger.Info("Custom prompts loaded successfully", "total_count", promptCount)
	}

	logger.Info("==========================================")
}
