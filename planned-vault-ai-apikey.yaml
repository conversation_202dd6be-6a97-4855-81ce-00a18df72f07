# Clean Vault Integration Configuration
# Focus: Optimal AI API Key Management Architecture

vault:
  # Core connection settings
  enabled: true
  address: "https://vault.company.com:8200"
  namespace: "resumatter"
  
  # Authentication configuration
  auth:
    method: "token"                    # token (the only one supported for now), approle, kubernetes, aws
    token: ""                          # Direct token
    tokenFile: "/var/secrets/vault-token"
    
    # AppRole authentication
    appRole:
      roleId: ""
      secretId: ""
      secretIdFile: ""
    
    # Kubernetes authentication  
    kubernetes:
      role: "resumatter"
      serviceAccountTokenPath: "/var/run/secrets/kubernetes.io/serviceaccount/token"

  # AI API Key Management - Clean Hierarchical Structure
  ai:
    # Default configuration for all operations
    default:
      path: "secret/data/resumatter/ai"
      key: "apiKey"
      provider: "gemini"               # Default provider (the only one supported for now)
    
    # Operation-specific configurations
    operations:
      tailor:
        path: "secret/data/resumatter/ai/tailor"
        key: "apiKey"  # this is the K of KV(v2 only), the V is what we're retrieving
        provider: "gemini"
        
        # Provider-specific overrides
        providers:
          gemini:
            path: "secret/data/resumatter/ai/tailor/gemini"
            key: "apiKey"
          openai:
            path: "secret/data/resumatter/ai/tailor/openai"  
            key: "apiKey"
          claude:
            path: "secret/data/resumatter/ai/tailor/claude"
            key: "apiKey"
      
      evaluate:
        path: "secret/data/resumatter/ai/evaluate"
        key: "apiKey"
        provider: "gemini"
        providers:
          gemini:
            path: "secret/data/resumatter/ai/evaluate/gemini"
            key: "apiKey"
      
      analyze:
        path: "secret/data/resumatter/ai/analyze"
        key: "apiKey"
        provider: "gemini"
        providers:
          gemini:
            path: "secret/data/resumatter/ai/analyze/gemini"
            key: "apiKey"

  # Server authentication
  server:
    path: "secret/data/resumatter/server"
    key: "apiKeys"
    format: "comma-separated"

  # TLS certificate management
  tls:
    path: "secret/data/resumatter/tls"
    keys:
      cert: "cert"
      key: "key" 
      ca: "ca"
    watcher:
      enabled: true
      pollInterval: "5m"
      renewThreshold: "24h"

  # Connection and performance settings
  connection:
    timeout: "30s"
    retries: 3
    retryDelay: "5s"
  
  # Caching for performance
  cache:
    enabled: true
    ttl: "5m"
    maxSize: 100
  
  # Health monitoring
  health:
    enabled: true
    checkInterval: "30s"
    failureThreshold: 3

# Environment Variable Mapping:
# RESUMATTER_VAULT_ENABLED=true
# RESUMATTER_VAULT_ADDRESS=https://vault.company.com:8200
# RESUMATTER_VAULT_AUTH_METHOD=token
# RESUMATTER_VAULT_AUTH_TOKENFILE=/var/secrets/vault-token
# RESUMATTER_VAULT_AI_DEFAULT_PATH=secret/data/resumatter/ai
# RESUMATTER_VAULT_AI_OPERATIONS_TAILOR_PROVIDER=openai
# RESUMATTER_VAULT_AI_OPERATIONS_TAILOR_PROVIDERS_OPENAI_PATH=secret/data/resumatter/ai/tailor/openai