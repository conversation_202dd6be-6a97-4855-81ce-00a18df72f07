# Reminders

## no emoji in codebase!
emoji causes error in find-replace tool resulting in truncated/empty file.

## docker-compose is not available
only docker and podman

## project root dir is /tmp/u/resumatter-public
common mistakes:
`cd /workspace && go run somefile.go`
`cd /tmp && go mod tidy`
`cd /tmp && go vet`

## modernize: interface{} -> any
`map[string]interface{}` -> `map[string]any`

## format code
`go fmt ./...`
be careful with your edits after this, source code rearranged.

## lint
`golangci-lint run`
or
`make lint`

## quick reference access
`go doc pkgname`

## package dependencies
`go get pkgname` instead of editing go.mod
or just use it in code then `go mod tidy`

