diff --git a/config.example.yaml b/config.example.yaml
index 17231e1..cec218b 100644
--- a/config.example.yaml
+++ b/config.example.yaml
@@ -159,6 +159,20 @@ observability:
     enabled: true
     collectionInterval: "15s"
 
+  # Logs configuration
+  logs:
+    enabled: true
+    level: "info"  # debug, info, warn, error
+    exporters:
+      console:
+        enabled: false  # Set to true for development debugging
+        prettyPrint: true
+      otlp:
+        enabled: false  # Set to true to send logs to OTLP collector
+        logsPath: "/v1/logs"  # OTLP logs endpoint path
+    correlation:
+      traceContext: true  # Add trace context (trace ID, span ID) to log records
+
   # Console output (development)
   console:
     enabled: false  # Set to true for development debugging
@@ -171,13 +185,14 @@ observability:
     port: "9090"
 
   # OTLP exporter (for Jaeger, Zipkin, cloud providers, etc.)
-  # Exports both traces and metrics to OTLP-compatible backends
+  # Exports traces, metrics, and logs to OTLP-compatible backends
   otlp:
     enabled: false
     endpoint: "localhost:4318"         # Host:port format only (no protocol/path)
     insecure: true                     # Use HTTP instead of HTTPS (dev only)
     tracesPath: "/v1/traces"           # Custom path for traces (default: /v1/traces, Grafana: /otlp/v1/traces)
     metricsPath: "/v1/metrics"         # Custom path for metrics (default: /v1/metrics, Grafana: /otlp/v1/metrics)
+    logsPath: "/v1/logs"               # Custom path for logs (default: /v1/logs, Grafana: /otlp/v1/logs)
     headers:                           # Custom headers for authentication
       # authorization: "Bearer your-token"
       # x-api-key: "your-api-key"
diff --git a/internal/config/config.go b/internal/config/config.go
index 05f6c35..353a3c4 100644
--- a/internal/config/config.go
+++ b/internal/config/config.go
@@ -189,6 +189,7 @@ type ObservabilityConfig struct {
 	SampleRate      float64             `mapstructure:"sampleRate"`
 	Tracing         TracingConfig       `mapstructure:"tracing"`
 	Metrics         MetricsConfig       `mapstructure:"metrics"`
+	Logs            LogsConfig          `mapstructure:"logs"`
 	CustomMetrics   CustomMetricsConfig `mapstructure:"customMetrics"`
 	Console         ConsoleConfig       `mapstructure:"console"`
 	Prometheus      PrometheusConfig    `mapstructure:"prometheus"`
@@ -208,6 +209,37 @@ type MetricsConfig struct {
 	CollectionInterval time.Duration `mapstructure:"collectionInterval"`
 }
 
+// LogsConfig holds logs configuration
+type LogsConfig struct {
+	Enabled     bool              `mapstructure:"enabled"`
+	Level       string            `mapstructure:"level"`
+	Exporters   LogExportersConfig `mapstructure:"exporters"`
+	Correlation LogCorrelationConfig `mapstructure:"correlation"`
+}
+
+// LogExportersConfig holds log exporter configuration
+type LogExportersConfig struct {
+	Console LogConsoleExporterConfig `mapstructure:"console"`
+	OTLP    LogOTLPExporterConfig    `mapstructure:"otlp"`
+}
+
+// LogConsoleExporterConfig holds console log exporter configuration
+type LogConsoleExporterConfig struct {
+	Enabled     bool `mapstructure:"enabled"`
+	PrettyPrint bool `mapstructure:"prettyPrint"`
+}
+
+// LogOTLPExporterConfig holds OTLP log exporter configuration
+type LogOTLPExporterConfig struct {
+	Enabled  bool   `mapstructure:"enabled"`
+	LogsPath string `mapstructure:"logsPath"`
+}
+
+// LogCorrelationConfig holds log correlation configuration
+type LogCorrelationConfig struct {
+	TraceContext bool `mapstructure:"traceContext"`
+}
+
 // ConsoleConfig holds console output configuration
 type ConsoleConfig struct {
 	Enabled     bool `mapstructure:"enabled"`
@@ -258,6 +290,7 @@ type OTLPConfig struct {
 	Headers     map[string]string `mapstructure:"headers"`
 	TracesPath  string            `mapstructure:"tracesPath"`
 	MetricsPath string            `mapstructure:"metricsPath"`
+	LogsPath    string            `mapstructure:"logsPath"`
 }
 
 // HealthCheckConfig holds health check configuration
diff --git a/internal/config/config_defaults.go b/internal/config/config_defaults.go
index e654718..d05616a 100644
--- a/internal/config/config_defaults.go
+++ b/internal/config/config_defaults.go
@@ -146,6 +146,15 @@ func setDefaults(v *viper.Viper) {
 	v.SetDefault("observability.metrics.enabled", true)
 	v.SetDefault("observability.metrics.collectionInterval", 15*time.Second)
 
+	// Logs Configuration
+	v.SetDefault("observability.logs.enabled", true)
+	v.SetDefault("observability.logs.level", "info")
+	v.SetDefault("observability.logs.exporters.console.enabled", false)
+	v.SetDefault("observability.logs.exporters.console.prettyPrint", true)
+	v.SetDefault("observability.logs.exporters.otlp.enabled", false)
+	v.SetDefault("observability.logs.exporters.otlp.logsPath", "/v1/logs")
+	v.SetDefault("observability.logs.correlation.traceContext", true)
+
 	// Custom Metrics Configuration
 	v.SetDefault("observability.customMetrics.aiOperations.enabled", true)
 	v.SetDefault("observability.customMetrics.aiOperations.trackDuration", true)
@@ -173,6 +182,7 @@ func setDefaults(v *viper.Viper) {
 	v.SetDefault("observability.otlp.insecure", true)
 	v.SetDefault("observability.otlp.tracesPath", "/v1/traces")
 	v.SetDefault("observability.otlp.metricsPath", "/v1/metrics")
+	v.SetDefault("observability.otlp.logsPath", "/v1/logs")
 	v.SetDefault("observability.otlp.headers", map[string]string{})
 
 	// Health Check Configuration
diff --git a/internal/observability/config.go b/internal/observability/config.go
index 11c34d1..e361d39 100644
--- a/internal/observability/config.go
+++ b/internal/observability/config.go
@@ -19,7 +19,22 @@ func GetObservabilityConfig(cfg *config.Config, version string) ObservabilityCon
 			ConsoleOutput:  true, // Default to console output for fallback
 			PrettyPrint:    true,
 			SampleRate:     1.0,
-			Prometheus:     GetPrometheusConfig(cfg),
+			Logs: LogsObservabilityConfig{
+				Enabled: true,
+				Level:   "info",
+				Console: LogConsoleObservabilityConfig{
+					Enabled:     false,
+					PrettyPrint: true,
+				},
+				OTLP: LogOTLPObservabilityConfig{
+					Enabled:  false,
+					LogsPath: "/v1/logs",
+				},
+				Correlation: LogCorrelationObservabilityConfig{
+					TraceContext: true,
+				},
+			},
+			Prometheus: GetPrometheusConfig(cfg),
 		}
 	}
 
@@ -38,6 +53,21 @@ func GetObservabilityConfig(cfg *config.Config, version string) ObservabilityCon
 		ConsoleOutput:  obsConfig.ConsoleOutput,
 		PrettyPrint:    obsConfig.Console.PrettyPrint,
 		SampleRate:     obsConfig.SampleRate,
+		Logs: LogsObservabilityConfig{
+			Enabled: obsConfig.Logs.Enabled,
+			Level:   obsConfig.Logs.Level,
+			Console: LogConsoleObservabilityConfig{
+				Enabled:     obsConfig.Logs.Exporters.Console.Enabled,
+				PrettyPrint: obsConfig.Logs.Exporters.Console.PrettyPrint,
+			},
+			OTLP: LogOTLPObservabilityConfig{
+				Enabled:  obsConfig.Logs.Exporters.OTLP.Enabled,
+				LogsPath: obsConfig.Logs.Exporters.OTLP.LogsPath,
+			},
+			Correlation: LogCorrelationObservabilityConfig{
+				TraceContext: obsConfig.Logs.Correlation.TraceContext,
+			},
+		},
 		Prometheus: PrometheusConfig{
 			Enabled:  obsConfig.Prometheus.Enabled,
 			Endpoint: obsConfig.Prometheus.Endpoint,
diff --git a/internal/observability/otel.go b/internal/observability/otel.go
index 5dc8140..b21de5d 100644
--- a/internal/observability/otel.go
+++ b/internal/observability/otel.go
@@ -33,9 +33,36 @@ type ObservabilityConfig struct {
 	ConsoleOutput  bool
 	PrettyPrint    bool
 	SampleRate     float64
+	Logs           LogsObservabilityConfig
 	Prometheus     PrometheusConfig
 }
 
+// LogsObservabilityConfig holds log-specific observability configuration
+type LogsObservabilityConfig struct {
+	Enabled     bool
+	Level       string
+	Console     LogConsoleObservabilityConfig
+	OTLP        LogOTLPObservabilityConfig
+	Correlation LogCorrelationObservabilityConfig
+}
+
+// LogConsoleObservabilityConfig holds console log exporter configuration
+type LogConsoleObservabilityConfig struct {
+	Enabled     bool
+	PrettyPrint bool
+}
+
+// LogOTLPObservabilityConfig holds OTLP log exporter configuration
+type LogOTLPObservabilityConfig struct {
+	Enabled  bool
+	LogsPath string
+}
+
+// LogCorrelationObservabilityConfig holds log correlation configuration
+type LogCorrelationObservabilityConfig struct {
+	TraceContext bool
+}
+
 // Metrics holds all custom metrics for Resumatter
 type Metrics struct {
 	// AI operation metrics
