package main

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"syscall"

	"resumatter/internal/cli"
	"resumatter/internal/config"
	"resumatter/internal/errors"
)

func main() {
	// --- Stage 1: Bootstrap Logger ---
	// Create a simple, temporary logger for the startup process.
	// We use a TextHandler here for better readability during local startup.
	bootstrapLogger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{Level: slog.LevelInfo}))

	// Create a context that is canceled on interrupt signals
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	// --- Use Bootstrap Logger for Config Loading ---
	bootstrapLogger.Info("Starting resumatter application")
	cfg, err := config.LoadConfig(bootstrapLogger)
	if err != nil {
		// Use Fprintf for the final fatal error, as the logger might not even be initializable.
		fmt.Fprintf(os.Stderr, "FATAL: Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// --- Stage 2: Final Application Logger ---
	// Now that we have the config, create the final, fully-configured logger.
	logger, err := errors.New(cfg.App.LogLevel)
	if err != nil {
		bootstrapLogger.Error("Failed to initialize final logger", "error", err)
		os.Exit(1)
	}

	// --- Hand off to the application ---
	logger.Info("Configuration loaded, handing off to CLI executor",
		"version", cli.Version,
		"log_level", cfg.App.LogLevel,
		"ai_provider", cfg.AI.Provider)

	// Execute command with the final logger
	if err := cli.Execute(ctx, cfg, logger); err != nil {
		logger.LogError(err, "Application execution failed")
		os.Exit(1)
	}

	logger.Info("Application shut down gracefully")
}
